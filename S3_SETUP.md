# AWS S3 Integration for Image Uploads

This guide explains how to set up AWS S3 for storing donation images in your Sheepskin Collection Platform.

## Features

The S3 integration provides:

- **Secure Image Storage**: Images are stored in AWS S3 with proper access controls
- **Image Upload Component**: Drag-and-drop interface for uploading multiple images
- **File Validation**: Automatic validation of file types and sizes
- **Progress Tracking**: Real-time upload progress and error handling
- **Image Preview**: Preview images before and after upload

## AWS S3 Setup

### 1. Create an S3 Bucket

1. **Log into AWS Console**

   - Go to [AWS S3 Console](https://s3.console.aws.amazon.com/)
   - Click "Create bucket"

2. **Configure Bucket Settings**

   - **Bucket Name**: Choose a unique name (e.g., `sheepskin-app-images-prod`)
   - **Region**: Choose a region close to your users (e.g., `us-east-1`)
   - **Block Public Access**: Keep all options checked for security
   - **Bucket Versioning**: Enable if you want version history
   - **Server-side Encryption**: Enable for added security

3. **Set CORS Policy**
   - Go to bucket → Permissions → Cross-origin resource sharing (CORS)
   - Add this configuration:

```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "POST", "PUT"],
    "AllowedOrigins": ["https://your-domain.com", "http://localhost:3000"],
    "ExposeHeaders": ["ETag"]
  }
]
```

### 2. Create IAM Role for EC2 (Recommended for EC2 Deployment)

Since you're deploying on EC2, use IAM roles instead of access keys for better security:

1. **Go to IAM Console**

   - Navigate to [AWS IAM Console](https://console.aws.amazon.com/iam/)
   - Click "Roles" → "Create role"

2. **Create Role**

   - **Trusted entity type**: AWS service
   - **Use case**: EC2
   - Click "Next"

3. **Create Custom Policy**
   - Click "Create policy"
   - Use JSON tab and paste this policy:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["s3:PutObject", "s3:GetObject", "s3:DeleteObject"],
      "Resource": "arn:aws:s3:::your-bucket-name/*"
    }
  ]
}
```

- **Policy name**: `SheepskinS3Access`
- Click "Create policy"

4. **Attach Policy to Role**

   - Back in role creation, search for `SheepskinS3Access`
   - Select the policy and click "Next"
   - **Role name**: `SheepskinEC2S3Role`
   - Click "Create role"

5. **Attach Role to EC2 Instance**
   - Go to EC2 Console → Your instance
   - Actions → Security → Modify IAM role
   - Select `SheepskinEC2S3Role`
   - Click "Update IAM role"

### 3. Configure Environment Variables (EC2 with IAM Role)

For EC2 with IAM role, you only need these variables in your `.env` file:

```bash
# AWS S3 Configuration (No access keys needed with IAM role)
AWS_REGION="us-east-1"
AWS_S3_BUCKET_NAME="your-bucket-name"
# Note: AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY are not needed
```

### 4. Update Bucket Policy (Optional)

For public read access to images, add this bucket policy:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::your-bucket-name/*"
    }
  ]
}
```

## Usage

### In Donation Form

The image upload component is automatically included in the donation creation form:

1. **Upload Images**: Click "Add Images" to select multiple files
2. **Supported Formats**: JPEG, PNG, WebP
3. **File Size Limit**: 5MB per image
4. **Maximum Images**: 5 images per donation
5. **Preview**: See uploaded images with remove/retry options

### API Endpoints

- **POST** `/api/uploads/images` - Upload single image
- **POST** `/api/donations` - Create donation with image URLs

### Image Display

Images are automatically displayed in:

- Collector Dashboard (donation listings)
- Admin Dashboard (donation management)
- Donation details modals

## Security Considerations

1. **Private Bucket**: Keep bucket private and use signed URLs for temporary access
2. **Access Keys**: Never commit AWS keys to version control
3. **IAM Permissions**: Use least-privilege principle for IAM policies
4. **File Validation**: Server-side validation prevents malicious uploads
5. **Size Limits**: Enforce file size limits to prevent abuse

## Cost Optimization

1. **S3 Storage Classes**: Use Standard-IA for infrequently accessed images
2. **Lifecycle Policies**: Automatically move old images to cheaper storage
3. **CloudFront**: Use CDN for faster image delivery and reduced costs
4. **Compression**: Consider image compression before upload

## Troubleshooting

### Common Issues

1. **CORS Errors**

   - Check CORS policy in S3 bucket
   - Ensure your domain is in AllowedOrigins

2. **Access Denied**

   - Verify IAM permissions
   - Check bucket policy
   - Ensure access keys are correct

3. **Upload Failures**
   - Check file size limits
   - Verify file types are supported
   - Check network connectivity

### Debugging

Enable debug logging in your environment:

```bash
DEBUG=aws-sdk
```

## Production Deployment

For production deployment:

1. Use separate buckets for staging and production
2. Enable CloudTrail for audit logging
3. Set up monitoring with CloudWatch
4. Consider using IAM roles instead of access keys for EC2
5. Enable S3 Transfer Acceleration for global users

## Alternative: Using IAM Roles (Recommended for EC2)

If deploying on EC2, use IAM roles instead of access keys:

1. Create an IAM role with S3 permissions
2. Attach the role to your EC2 instance
3. Remove AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY from environment
4. AWS SDK will automatically use the instance role

This is more secure as it eliminates the need to store credentials in your application.
