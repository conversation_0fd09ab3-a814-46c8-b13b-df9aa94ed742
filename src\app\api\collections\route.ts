import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET /api/collections - Fetch collections for the current collector
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "COLLECTOR") {
      return NextResponse.json(
        { error: "Access denied. Collector role required." },
        { status: 403 }
      );
    }

    // Fetch collections for the current collector with donation details
    const collections = await prisma.collection.findMany({
      where: {
        collectorId: session.user.id,
      },
      include: {
        donation: {
          include: {
            donor: {
              select: {
                id: true,
                name: true,
                email: true,
                contact: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(collections);
  } catch (error) {
    console.error("Error fetching collections:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/collections - Claim a donation (create new collection)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "COLLECTOR") {
      return NextResponse.json(
        { error: "Access denied. Collector role required." },
        { status: 403 }
      );
    }

    const { donationId } = await request.json();

    if (!donationId) {
      return NextResponse.json(
        { error: "Donation ID is required" },
        { status: 400 }
      );
    }

    // Check if donation exists and is available
    const donation = await prisma.donation.findUnique({
      where: { id: donationId },
    });

    if (!donation) {
      return NextResponse.json(
        { error: "Donation not found" },
        { status: 404 }
      );
    }

    if (donation.status !== "AVAILABLE") {
      return NextResponse.json(
        { error: "Donation is not available for claiming" },
        { status: 400 }
      );
    }

    // Check if donation is already claimed by this collector
    const existingCollection = await prisma.collection.findFirst({
      where: {
        donationId: donationId,
        collectorId: session.user.id,
      },
    });

    if (existingCollection) {
      return NextResponse.json(
        { error: "You have already claimed this donation" },
        { status: 400 }
      );
    }

    // Use transaction to update donation status and create collection
    const result = await prisma.$transaction(async (tx) => {
      // Update donation status to CLAIMED
      await tx.donation.update({
        where: { id: donationId },
        data: { status: "CLAIMED" },
      });

      // Create new collection
      const collection = await tx.collection.create({
        data: {
          collectorId: session.user.id,
          donationId: donationId,
          status: "PENDING",
        },
        include: {
          donation: {
            include: {
              donor: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  contact: true,
                },
              },
            },
          },
        },
      });

      return collection;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error("Error claiming donation:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
