/**
 * OpenStreetMap configuration and utilities
 */

/**
 * Get OpenStreetMap configuration for MapLibre GL JS
 */
export const getMapConfig = () => {
  console.log("Using OpenStreetMap configuration");

  return {
    version: 8,
    sources: {
      "osm-tiles": {
        type: "raster",
        tiles: ["https://tile.openstreetmap.org/{z}/{x}/{y}.png"],
        tileSize: 256,
        attribution: "© OpenStreetMap contributors",
      },
    },
    layers: [
      {
        id: "osm-tiles",
        type: "raster",
        source: "osm-tiles",
        minzoom: 0,
        maxzoom: 19,
      },
    ],
  };
};

/**
 * Search for places by text using OpenStreetMap Nominatim API
 */
interface SearchByTextOptions {
  biasPosition?: [number, number];
  maxResults?: number;
  countries?: string[];
  language?: string;
}

interface SearchByTextResult {
  geometry: {
    point: [number, number];
  };
  label: string;
  country?: string;
  region?: string;
}

export const searchByText = async (
  text: string,
  options: SearchByTextOptions = {}
): Promise<SearchByTextResult[]> => {
  try {
    const { maxResults = 10, countries, language = "en" } = options;

    let url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
      text
    )}&limit=${maxResults}&addressdetails=1&accept-language=${language}`;

    if (countries && countries.length > 0) {
      url += `&countrycodes=${countries.join(",")}`;
    }

    const response = await fetch(url, {
      headers: {
        "User-Agent": "Sheepskin Collection Platform",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    return data.map((item: any) => ({
      geometry: {
        point: [parseFloat(item.lon), parseFloat(item.lat)],
      },
      label: item.display_name,
      country: item.address?.country,
      region: item.address?.state || item.address?.province,
    }));
  } catch (error) {
    console.error("Error searching by text:", error);
    return [];
  }
};

/**
 * Get coordinates from an address using OpenStreetMap Nominatim API
 */
interface GeocodeResult {
  longitude: number;
  latitude: number;
  label: string;
  country?: string;
  region?: string;
}

export const geocodeAddress = async (
  address: string
): Promise<GeocodeResult | null> => {
  try {
    const results = await searchByText(address, { maxResults: 1 });
    if (results && results.length > 0) {
      const place = results[0];
      if (place.geometry && Array.isArray(place.geometry.point)) {
        return {
          longitude: place.geometry.point[0],
          latitude: place.geometry.point[1],
          label: place.label,
          country: place.country,
          region: place.region,
        };
      }
    }
    return null;
  } catch (error) {
    console.error("Geocoding error:", error);
    return null;
  }
};

/**
 * Reverse geocode coordinates to get address using OpenStreetMap Nominatim API
 */
interface ReverseGeocodeResult {
  geometry: {
    point: [number, number];
  };
  label: string;
  country?: string;
  region?: string;
}

export const reverseGeocode = async (
  longitude: number,
  latitude: number
): Promise<ReverseGeocodeResult | null> => {
  try {
    const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&addressdetails=1`;

    const response = await fetch(url, {
      headers: {
        "User-Agent": "Sheepskin Collection Platform",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data && data.display_name) {
      return {
        geometry: {
          point: [longitude, latitude],
        },
        label: data.display_name,
        country: data.address?.country,
        region: data.address?.state || data.address?.province,
      };
    }

    return null;
  } catch (error) {
    console.error("Reverse geocoding error:", error);
    return null;
  }
};
