"use client";

import { useState, useEffect } from "react";
import ProtectedRoute from "@/components/ProtectedRoute";
import { useAuth } from "@/contexts/AuthContext";
import ImageUpload from "@/components/ui/ImageUpload";

export default function DonorDashboard() {
  const [language, setLanguage] = useState("en");
  const [activeTab, setActiveTab] = useState("dashboard");
  const [donations, setDonations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  const [newDonation, setNewDonation] = useState({
    numberOfHides: "",
    location: "",
    collectionTime: "",
    description: "",
    imageUrls: [],
  });

  const translations = {
    en: {
      dashboard: "Dashboard",
      newDonation: "New Donation",
      myDonations: "My Donations",
      profile: "Profile",
      donorDashboard: "Donor Dashboard",
      numberOfHides: "Number of Hides",
      location: "Location",
      preferredPickupTime: "Preferred Pickup Time",
      description: "Description (Optional)",
      images: "Images",
      submitDonation: "Submit Donation",
      id: "ID",
      status: "Status",
      pickupTime: "Pickup Time",
      actions: "Actions",
      edit: "Edit",
      delete: "Delete",
      available: "Available",
      collected: "Collected",
      claimed: "Claimed",
      totalDonations: "Total Donations",
      activeDonations: "Active Donations",
      completedDonations: "Completed Donations",
      loading: "Loading...",
      error: "Error",
      success: "Success",
      noDataAvailable: "No donations available",
    },
    fr: {
      dashboard: "Tableau de Bord",
      newDonation: "Nouveau Don",
      myDonations: "Mes Dons",
      profile: "Profil",
      donorDashboard: "Tableau de Bord Donateur",
      numberOfHides: "Nombre de Peaux",
      location: "Emplacement",
      preferredPickupTime: "Heure de Ramassage Préférée",
      description: "Description (Optionnel)",
      images: "Images",
      submitDonation: "Soumettre le Don",
      id: "ID",
      status: "Statut",
      pickupTime: "Heure de Ramassage",
      actions: "Actions",
      edit: "Modifier",
      delete: "Supprimer",
      available: "Disponible",
      collected: "Collecté",
      claimed: "Réclamé",
      totalDonations: "Total des Dons",
      activeDonations: "Dons Actifs",
      completedDonations: "Dons Terminés",
      loading: "Chargement...",
      error: "Erreur",
      success: "Succès",
      noDataAvailable: "Aucun don disponible",
    },
    ar: {
      dashboard: "لوحة التحكم",
      newDonation: "تبرع جديد",
      myDonations: "تبرعاتي",
      profile: "الملف الشخصي",
      donorDashboard: "لوحة تحكم المتبرع",
      numberOfHides: "عدد الجلود",
      location: "الموقع",
      preferredPickupTime: "وقت الاستلام المفضل",
      description: "الوصف (اختياري)",
      images: "الصور",
      submitDonation: "إرسال التبرع",
      id: "المعرف",
      status: "الحالة",
      pickupTime: "وقت الاستلام",
      actions: "الإجراءات",
      edit: "تعديل",
      delete: "حذف",
      available: "متاح",
      collected: "تم الجمع",
      claimed: "مطالب به",
      totalDonations: "إجمالي التبرعات",
      activeDonations: "التبرعات النشطة",
      completedDonations: "التبرعات المكتملة",
      loading: "جارٍ التحميل...",
      error: "خطأ",
      success: "نجح",
      noDataAvailable: "لا توجد تبرعات متاحة",
    },
  };

  const t = (key) => translations[language][key] || key;

  // Fetch donations from API
  const fetchDonations = async () => {
    try {
      setLoading(true);
      setError("");

      const response = await fetch("/api/donations", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch donations");
      }

      const data = await response.json();
      setDonations(data.donations || []);
    } catch (error) {
      console.error("Error fetching donations:", error);
      setError(error.message || "Failed to load donations");
    } finally {
      setLoading(false);
    }
  };

  // Load donations when component mounts
  useEffect(() => {
    fetchDonations();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    // Validate inputs
    if (
      !newDonation.numberOfHides ||
      !newDonation.location ||
      !newDonation.collectionTime
    ) {
      setError("All fields are required");
      return;
    }

    const hidesNum = parseInt(newDonation.numberOfHides);
    if (isNaN(hidesNum) || hidesNum <= 0) {
      setError("Number of hides must be a positive integer");
      return;
    }

    if (newDonation.location.trim().length === 0) {
      setError("Location cannot be empty");
      return;
    }

    const collectionDate = new Date(newDonation.collectionTime);
    if (isNaN(collectionDate.getTime())) {
      setError("Invalid collection time format");
      return;
    }

    if (collectionDate <= new Date()) {
      setError("Collection time must be in the future");
      return;
    }

    try {
      setLoading(true);

      const response = await fetch("/api/donations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          numberOfHides: hidesNum,
          location: newDonation.location.trim(),
          collectionTime: collectionDate.toISOString(),
          description: newDonation.description.trim() || null,
          imageUrls: newDonation.imageUrls,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create donation");
      }

      const data = await response.json();
      setSuccess("Donation created successfully!");
      setNewDonation({
        numberOfHides: "",
        location: "",
        collectionTime: "",
        description: "",
        imageUrls: [],
      });

      // Refresh donations list
      await fetchDonations();
    } catch (error) {
      console.error("Error creating donation:", error);
      setError(error.message || "Failed to create donation");
    } finally {
      setLoading(false);
    }
  };

  const deleteDonation = async (id) => {
    if (!confirm("Are you sure you want to delete this donation?")) {
      return;
    }

    try {
      setError("");
      const response = await fetch(`/api/donations/${id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete donation");
      }

      setSuccess("Donation deleted successfully!");
      // Refresh donations list
      await fetchDonations();
    } catch (error) {
      console.error("Error deleting donation:", error);
      setError(error.message || "Failed to delete donation");
    }
  };

  const stats = {
    total: donations.length,
    active: donations.filter(
      (d) => d.status === "AVAILABLE" || d.status === "CLAIMED"
    ).length,
    completed: donations.filter((d) => d.status === "COLLECTED").length,
  };

  const { user, signOut } = useAuth();

  return (
    <ProtectedRoute requiredRole="DONOR">
      <div
        className={`min-h-screen bg-gray-50 ${
          language === "ar" ? "rtl" : "ltr"
        }`}
      >
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white shadow-lg min-h-screen">
            <div className="p-6">
              <h1 className="text-2xl font-bold text-gray-900">
                {t("donorDashboard")}
              </h1>
              {user && (
                <p className="text-sm text-gray-600 mt-1">
                  Welcome, {user.name}
                </p>
              )}

              {/* Language Selector */}
              <div className="mt-4">
                <select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  className="w-full bg-white border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
                  aria-label="Select language"
                >
                  <option value="en">English</option>
                  <option value="fr">Français</option>
                  <option value="ar">العربية</option>
                </select>
              </div>
            </div>

            <nav className="mt-6">
              {["dashboard", "newDonation", "myDonations", "profile"].map(
                (tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`w-full text-left px-6 py-3 text-sm font-medium transition-colors ${
                      activeTab === tab
                        ? "bg-green-50 text-green-700 border-r-2 border-green-500"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                    }`}
                    aria-label={t(tab)}
                  >
                    {t(tab)}
                  </button>
                )
              )}
              <button
                onClick={signOut}
                className="w-full text-left px-6 py-3 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700"
              >
                Logout
              </button>
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-8">
            {/* Error and Success Messages */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                {error}
              </div>
            )}
            {success && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
                {success}
              </div>
            )}

            {loading && (
              <div className="text-center py-4">
                <div className="text-gray-600">{t("loading")}</div>
              </div>
            )}
            {activeTab === "dashboard" && (
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">
                  {t("dashboard")}
                </h2>

                {/* Stats Cards */}
                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-700">
                      {t("totalDonations")}
                    </h3>
                    <p className="text-3xl font-bold text-green-600">
                      {stats.total}
                    </p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-700">
                      {t("activeDonations")}
                    </h3>
                    <p className="text-3xl font-bold text-blue-600">
                      {stats.active}
                    </p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-700">
                      {t("completedDonations")}
                    </h3>
                    <p className="text-3xl font-bold text-gray-600">
                      {stats.completed}
                    </p>
                  </div>
                </div>

                {/* Recent Donations */}
                <div className="bg-white rounded-lg shadow">
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      {t("myDonations")}
                    </h3>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left py-3 px-4 font-semibold text-gray-700">
                              {t("id")}
                            </th>
                            <th className="text-left py-3 px-4 font-semibold text-gray-700">
                              {t("numberOfHides")}
                            </th>
                            <th className="text-left py-3 px-4 font-semibold text-gray-700">
                              {t("location")}
                            </th>
                            <th className="text-left py-3 px-4 font-semibold text-gray-700">
                              {t("status")}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {donations.length === 0 ? (
                            <tr>
                              <td
                                colSpan="4"
                                className="py-8 px-4 text-center text-gray-500"
                              >
                                {t("noDataAvailable")}
                              </td>
                            </tr>
                          ) : (
                            donations.slice(0, 3).map((donation) => (
                              <tr
                                key={donation.id}
                                className="border-b hover:bg-gray-50"
                              >
                                <td className="py-3 px-4">{donation.id}</td>
                                <td className="py-3 px-4">
                                  {donation.numberOfHides}
                                </td>
                                <td className="py-3 px-4">
                                  {donation.location}
                                </td>
                                <td className="py-3 px-4">
                                  <span
                                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                                      donation.status === "AVAILABLE"
                                        ? "bg-green-100 text-green-800"
                                        : donation.status === "CLAIMED"
                                        ? "bg-yellow-100 text-yellow-800"
                                        : "bg-gray-100 text-gray-800"
                                    }`}
                                  >
                                    {t(donation.status.toLowerCase())}
                                  </span>
                                </td>
                              </tr>
                            ))
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "newDonation" && (
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">
                  {t("newDonation")}
                </h2>

                <div className="bg-white rounded-lg shadow p-6">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                      <label
                        htmlFor="numberOfHides"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        {t("numberOfHides")}
                      </label>
                      <input
                        type="number"
                        id="numberOfHides"
                        min="1"
                        value={newDonation.numberOfHides}
                        onChange={(e) =>
                          setNewDonation({
                            ...newDonation,
                            numberOfHides: e.target.value,
                          })
                        }
                        className="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-green-500"
                        placeholder="Enter number of hides"
                        required
                        disabled={loading}
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="location"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        {t("location")}
                      </label>
                      <input
                        type="text"
                        id="location"
                        value={newDonation.location}
                        onChange={(e) =>
                          setNewDonation({
                            ...newDonation,
                            location: e.target.value,
                          })
                        }
                        className="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-green-500"
                        placeholder="Enter your location"
                        required
                        disabled={loading}
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="collectionTime"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        {t("preferredPickupTime")}
                      </label>
                      <input
                        type="datetime-local"
                        id="collectionTime"
                        value={newDonation.collectionTime}
                        onChange={(e) =>
                          setNewDonation({
                            ...newDonation,
                            collectionTime: e.target.value,
                          })
                        }
                        className="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-green-500"
                        required
                        disabled={loading}
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="description"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        {t("description")}
                      </label>
                      <textarea
                        id="description"
                        value={newDonation.description}
                        onChange={(e) =>
                          setNewDonation({
                            ...newDonation,
                            description: e.target.value,
                          })
                        }
                        className="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-green-500"
                        placeholder="Describe the condition and details of your sheep hides (optional)"
                        rows={3}
                        disabled={loading}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t("images")}
                      </label>
                      <ImageUpload
                        onImagesUploaded={(imageUrls) =>
                          setNewDonation({
                            ...newDonation,
                            imageUrls,
                          })
                        }
                        maxImages={5}
                        className="mb-4"
                      />
                    </div>

                    <button
                      type="submit"
                      className="bg-green-500 hover:bg-green-600 text-white py-3 px-6 rounded-lg font-semibold transition-colors duration-200 w-full md:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
                      aria-label={t("submitDonation")}
                      disabled={loading}
                    >
                      {loading ? t("loading") : t("submitDonation")}
                    </button>
                  </form>
                </div>
              </div>
            )}

            {activeTab === "myDonations" && (
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">
                  {t("myDonations")}
                </h2>

                <div className="bg-white rounded-lg shadow">
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b bg-gray-50">
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("id")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("numberOfHides")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("location")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("status")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("pickupTime")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("actions")}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {donations.length === 0 ? (
                          <tr>
                            <td
                              colSpan="6"
                              className="py-8 px-6 text-center text-gray-500"
                            >
                              {t("noDataAvailable")}
                            </td>
                          </tr>
                        ) : (
                          donations.map((donation) => (
                            <tr
                              key={donation.id}
                              className="border-b hover:bg-gray-50"
                            >
                              <td className="py-4 px-6">{donation.id}</td>
                              <td className="py-4 px-6">
                                {donation.numberOfHides}
                              </td>
                              <td className="py-4 px-6">{donation.location}</td>
                              <td className="py-4 px-6">
                                <span
                                  className={`px-3 py-1 rounded-full text-sm font-medium ${
                                    donation.status === "AVAILABLE"
                                      ? "bg-green-100 text-green-800"
                                      : donation.status === "CLAIMED"
                                      ? "bg-yellow-100 text-yellow-800"
                                      : "bg-gray-100 text-gray-800"
                                  }`}
                                >
                                  {t(donation.status.toLowerCase())}
                                </span>
                              </td>
                              <td className="py-4 px-6">
                                {new Date(
                                  donation.collectionTime
                                ).toLocaleString()}
                              </td>
                              <td className="py-4 px-6">
                                <div className="flex space-x-2">
                                  <button
                                    className="text-blue-600 hover:text-blue-800 font-medium"
                                    aria-label={`${t("edit")} donation ${
                                      donation.id
                                    }`}
                                  >
                                    {t("edit")}
                                  </button>
                                  <button
                                    onClick={() => deleteDonation(donation.id)}
                                    className="text-red-600 hover:text-red-800 font-medium"
                                    aria-label={`${t("delete")} donation ${
                                      donation.id
                                    }`}
                                  >
                                    {t("delete")}
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "profile" && (
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">
                  {t("profile")}
                </h2>
                <div className="bg-white rounded-lg shadow p-6">
                  <p className="text-gray-600">
                    Profile settings will be implemented here.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
