import NextAuth, { DefaultSession } from "next-auth";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      role: string;
      contact?: string;
      location?: string;
    } & DefaultSession["user"];
  }

  interface User {
    role: string;
    contact?: string;
    location?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: string;
    contact?: string;
    location?: string;
  }
}
