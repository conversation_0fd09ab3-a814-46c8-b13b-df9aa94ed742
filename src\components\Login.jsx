"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import Link from "next/link";

export default function Login() {
  const [language, setLanguage] = useState("en");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const { signIn, user } = useAuth();
  const router = useRouter();

  // Redirect if user is already authenticated
  useEffect(() => {
    if (user) {
      // Role-based redirection
      switch (user.role) {
        case "DONOR":
          router.replace("/donor-dashboard");
          break;
        case "COLLECTOR":
          router.replace("/collector-dashboard");
          break;
        case "ADMIN":
          router.replace("/admin-dashboard");
          break;
        default:
          router.replace("/");
          break;
      }
    }
  }, [user, router]);

  const translations = {
    en: {
      title: "Sign In",
      subtitle: "Welcome back to the Sheepskin Collection Platform",
      email: "Email Address",
      password: "Password",
      signIn: "Sign In",
      noAccount: "Don't have an account?",
      signUp: "Sign Up",
      backToHome: "Back to Home",
      emailPlaceholder: "Enter your email",
      passwordPlaceholder: "Enter your password",
      signingIn: "Signing in...",
      invalidCredentials: "Invalid email or password",
      networkError: "Network error. Please try again.",
    },
    fr: {
      title: "Se Connecter",
      subtitle: "Bienvenue sur la Plateforme de Collecte de Peaux",
      email: "Adresse Email",
      password: "Mot de Passe",
      signIn: "Se Connecter",
      noAccount: "Vous n'avez pas de compte?",
      signUp: "S'inscrire",
      backToHome: "Retour à l'Accueil",
      emailPlaceholder: "Entrez votre email",
      passwordPlaceholder: "Entrez votre mot de passe",
      signingIn: "Connexion...",
      invalidCredentials: "Email ou mot de passe invalide",
      networkError: "Erreur réseau. Veuillez réessayer.",
    },
    ar: {
      title: "تسجيل الدخول",
      subtitle: "مرحباً بك في منصة جمع جلود الأضاحي",
      email: "عنوان البريد الإلكتروني",
      password: "كلمة المرور",
      signIn: "تسجيل الدخول",
      noAccount: "ليس لديك حساب؟",
      signUp: "إنشاء حساب",
      backToHome: "العودة للرئيسية",
      emailPlaceholder: "أدخل بريدك الإلكتروني",
      passwordPlaceholder: "أدخل كلمة المرور",
      signingIn: "جاري تسجيل الدخول...",
      invalidCredentials: "بريد إلكتروني أو كلمة مرور غير صحيحة",
      networkError: "خطأ في الشبكة. يرجى المحاولة مرة أخرى.",
    },
  };

  const t = (key) => translations[language][key] || key;
  const handleSubmit = async (e) => {
    e.preventDefault();

    setLoading(true);
    setError("");

    const result = await signIn(email, password);

    if (result?.error) {
      setError(t("invalidCredentials"));
      setLoading(false);
    } else {
      // Successful login - the useEffect will handle redirection
      setLoading(false);
    }
  };

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-amber-50 to-orange-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${
        language === "ar" ? "rtl" : "ltr"
      }`}
    >
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-gray-900">{t("title")}</h2>
          <p className="mt-2 text-sm text-gray-600">{t("subtitle")}</p>
        </div>

        {/* Language Selector */}
        <div className="flex justify-center">
          <select
            value={language}
            onChange={(e) => setLanguage(e.target.value)}
            className="bg-white border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500"
            aria-label="Select language"
          >
            <option value="en">English</option>
            <option value="fr">Français</option>
            <option value="ar">العربية</option>
          </select>
        </div>

        <form
          className="bg-white p-8 rounded-xl shadow-lg space-y-6"
          onSubmit={handleSubmit}
        >
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
              {error}
            </div>
          )}

          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              {t("email")}
            </label>
            <input
              id="email"
              name="email"
              type="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              placeholder={t("emailPlaceholder")}
            />
          </div>

          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              {t("password")}
            </label>
            <input
              id="password"
              name="password"
              type="password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              placeholder={t("passwordPlaceholder")}
            />
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-amber-600 hover:bg-amber-700 disabled:bg-amber-400 text-white py-3 px-4 rounded-lg font-semibold transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2"
            >
              {loading ? t("signingIn") : t("signIn")}
            </button>
          </div>

          <div className="text-center space-y-2">
            <p className="text-sm text-gray-600">
              {t("noAccount")}{" "}
              <Link
                href="/register"
                className="text-amber-600 hover:text-amber-700 font-medium"
              >
                {t("signUp")}
              </Link>
            </p>
            <Link
              href="/"
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              {t("backToHome")}
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}
