"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

export default function RoleBasedRedirect() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        // User is not authenticated, stay on home page
        return;
      }

      // Role-based redirection for authenticated users
      switch (user.role) {
        case "DONOR":
          router.replace("/donor-dashboard");
          break;
        case "COLLECTOR":
          router.replace("/collector-dashboard");
          break;
        case "ADMIN":
          router.replace("/admin-dashboard");
          break;
        default:
          // Unknown role, stay on home page
          break;
      }
    }
  }, [user, loading, router]);

  // Return null as this is just a redirect component
  return null;
}
