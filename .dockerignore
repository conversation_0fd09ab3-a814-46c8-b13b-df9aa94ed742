# Docker ignore file for Next.js application
# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/

# Production build
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# VS Code
.vscode/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Testing
coverage/
.nyc_output

# Prisma
prisma/migrations/

# Scripts
scripts/

# Documentation
README.md

# EC2 key files
*.pem

# Jest
jest.config.js
jest.setup.js

# Other config files
.eslintrc.json
tailwind.config.ts
postcss.config.mjs
components.json
