/**
 * @jest-environment jsdom
 */
import { render } from "@testing-library/react";
import "@testing-library/jest-dom";

// Simple component test without external dependencies
describe("Basic Component Tests", () => {
  it("should run Jest tests successfully", () => {
    const testDiv = document.createElement("div");
    testDiv.textContent = "Test content";
    document.body.appendChild(testDiv);
    expect(testDiv).toBeInTheDocument();
    document.body.removeChild(testDiv);
  });

  it("should handle basic DOM operations", () => {
    const { container } = render(<div>Hello World</div>);
    expect(container.firstChild).toHaveTextContent("Hello World");
  });
});

// Simple environment test
describe("Environment Configuration", () => {
  it("should have test environment variables", () => {
    expect(process.env.NEXTAUTH_URL).toBe("http://localhost:3000");
    expect(process.env.NEXTAUTH_SECRET).toBe("test-secret");
  });
});
