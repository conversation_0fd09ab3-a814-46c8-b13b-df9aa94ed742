{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:seed": "tsx prisma/seed.ts", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset --force", "db:push": "prisma db push", "postinstall": "prisma generate"}, "dependencies": {"@aws-sdk/client-s3": "^3.850.0", "@aws-sdk/s3-request-presigner": "^3.850.0", "@hookform/resolvers": "^3.9.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "maplibre-gl": "^2.4.0", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "react": "^19", "react-day-picker": "^9.2.2", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.1", "zod": "^3.24.1"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/bcryptjs": "^3.0.0", "@types/jest": "^29.5.14", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint-config-next": "^15.2.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5", "prisma": "^6.11.1", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5"}}