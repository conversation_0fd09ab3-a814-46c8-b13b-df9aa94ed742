"use client";

import React, { createContext, useContext } from "react";
import { useSession, signIn, signOut } from "next-auth/react";

interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  role?: string;
  contact?: string;
  location?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signUp: (
    email: string,
    password: string,
    userData: {
      full_name: string;
      role: string;
      phone?: string;
      location?: string;
    }
  ) => Promise<{ error?: any; success?: boolean }>;
  signIn: (
    email: string,
    password: string
  ) => Promise<{ error?: any; success?: boolean }>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const loading = status === "loading";

  // Map NextAuth session to our interface
  const user: User | null = session?.user
    ? {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        role: session.user.role,
        contact: session.user.contact,
        location: session.user.location,
      }
    : null;

  const handleSignUp = async (
    email: string,
    password: string,
    userData: {
      full_name: string;
      role: string;
      phone?: string;
      location?: string;
    }
  ) => {
    try {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: userData.full_name,
          email,
          password,
          role: userData.role,
          contact: userData.phone,
          location: userData.location,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        return { error: result.error || "Registration failed" };
      }

      return { success: true };
    } catch (error) {
      console.error("Registration error:", error);
      return { error: "Network error occurred" };
    }
  };

  const handleSignIn = async (email: string, password: string) => {
    try {
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        return { error: result.error };
      }

      return { success: true };
    } catch (error) {
      console.error("Sign in error:", error);
      return { error: "Sign in failed" };
    }
  };

  const handleSignOut = async () => {
    await signOut({ redirect: false });
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        signUp: handleSignUp,
        signIn: handleSignIn,
        signOut: handleSignOut,
        isAuthenticated: !!session?.user,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
