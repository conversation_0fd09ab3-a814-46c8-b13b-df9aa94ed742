#!/bin/bash

# Local development and testing script for Sheepskin Collection Platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Sheepskin Collection Platform - Development Script${NC}"
echo ""

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from .env.example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_status ".env file created. Please update it with your configuration."
    else
        print_error ".env.example not found. Please create .env manually."
        exit 1
    fi
fi

# Check Node.js version
NODE_VERSION=$(node --version)
echo "Node.js version: $NODE_VERSION"

# Install dependencies
echo ""
echo -e "${BLUE}📦 Installing dependencies...${NC}"
npm install
print_status "Dependencies installed"

# Generate Prisma client
echo ""
echo -e "${BLUE}🔧 Generating Prisma client...${NC}"
npx prisma generate
print_status "Prisma client generated"

# Check if database is accessible
echo ""
echo -e "${BLUE}🗄️ Checking database connection...${NC}"
if npx prisma db pull > /dev/null 2>&1; then
    print_status "Database connection successful"
else
    print_warning "Database connection failed. Make sure PostgreSQL is running and DATABASE_URL is correct."
    echo "You can run: npx prisma migrate dev"
fi

# Run linting
echo ""
echo -e "${BLUE}🔍 Running linter...${NC}"
if npm run lint; then
    print_status "Linting passed"
else
    print_warning "Linting failed. Please fix the issues above."
fi

# Run type checking
echo ""
echo -e "${BLUE}📝 Running TypeScript check...${NC}"
if npx tsc --noEmit; then
    print_status "TypeScript check passed"
else
    print_error "TypeScript check failed. Please fix the type errors above."
fi

# Run tests
echo ""
echo -e "${BLUE}🧪 Running tests...${NC}"
if npm test; then
    print_status "All tests passed"
else
    print_warning "Some tests failed. Please check the output above."
fi

# Build the application
echo ""
echo -e "${BLUE}🏗️ Building application...${NC}"
if npm run build; then
    print_status "Build successful"
else
    print_error "Build failed. Please fix the issues above."
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Development setup complete!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Start development server: npm run dev"
echo "2. Open http://localhost:3000 in your browser"
echo "3. Make your changes and test"
echo "4. Commit and push to trigger CI/CD pipeline"
echo ""
echo -e "${BLUE}Useful commands:${NC}"
echo "• npm run dev          - Start development server"
echo "• npm run build        - Build for production"
echo "• npm test             - Run tests"
echo "• npm run lint         - Run linter"
echo "• npx prisma studio    - Open Prisma database browser"
echo "• npx prisma migrate dev - Run database migrations"
