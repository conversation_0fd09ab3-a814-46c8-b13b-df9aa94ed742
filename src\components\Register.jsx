"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import Link from "next/link";

export default function Register() {
  const [language, setLanguage] = useState("en");
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    fullName: "",
    role: "DONOR",
    phone: "",
    location: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  const { signUp, user } = useAuth();
  const router = useRouter();

  // Redirect if user is already authenticated
  useEffect(() => {
    if (user) {
      // Role-based redirection
      switch (user.role) {
        case "DONOR":
          router.replace("/donor-dashboard");
          break;
        case "COLLECTOR":
          router.replace("/collector-dashboard");
          break;
        case "ADMIN":
          router.replace("/admin-dashboard");
          break;
        default:
          router.replace("/");
          break;
      }
    }
  }, [user, router]);

  const translations = {
    en: {
      title: "Create Account",
      subtitle: "Join the Sheepskin Collection Platform",
      email: "Email Address",
      password: "Password",
      confirmPassword: "Confirm Password",
      fullName: "Full Name",
      role: "I am a",
      donor: "Donor (I have sheepskin to donate)",
      collector: "Collector (I collect sheepskin)",
      phone: "Phone Number (Optional)",
      location: "Location",
      signUp: "Create Account",
      haveAccount: "Already have an account?",
      signIn: "Sign In",
      backToHome: "Back to Home",
      creating: "Creating account...",
      passwordMismatch: "Passwords do not match",
      weakPassword: "Password should be at least 6 characters",
      emailExists: "An account with this email already exists",
      networkError: "Network error. Please try again.",
      successMessage:
        "Account created successfully! Please check your email to verify your account.",
      emailPlaceholder: "Enter your email",
      passwordPlaceholder: "Enter your password",
      confirmPasswordPlaceholder: "Confirm your password",
      fullNamePlaceholder: "Enter your full name",
      phonePlaceholder: "Enter your phone number",
      locationPlaceholder: "Enter your location",
    },
    fr: {
      title: "Créer un Compte",
      subtitle: "Rejoignez la Plateforme de Collecte de Peaux",
      email: "Adresse Email",
      password: "Mot de Passe",
      confirmPassword: "Confirmer le Mot de Passe",
      fullName: "Nom Complet",
      role: "Je suis un",
      donor: "Donateur (J'ai des peaux à donner)",
      collector: "Collecteur (Je collecte des peaux)",
      phone: "Numéro de Téléphone (Optionnel)",
      location: "Emplacement",
      signUp: "Créer un Compte",
      haveAccount: "Vous avez déjà un compte?",
      signIn: "Se Connecter",
      backToHome: "Retour à l'Accueil",
      creating: "Création du compte...",
      passwordMismatch: "Les mots de passe ne correspondent pas",
      weakPassword: "Le mot de passe doit contenir au moins 6 caractères",
      emailExists: "Un compte avec cet email existe déjà",
      networkError: "Erreur réseau. Veuillez réessayer.",
      successMessage:
        "Compte créé avec succès! Veuillez vérifier votre email pour confirmer votre compte.",
      emailPlaceholder: "Entrez votre email",
      passwordPlaceholder: "Entrez votre mot de passe",
      confirmPasswordPlaceholder: "Confirmez votre mot de passe",
      fullNamePlaceholder: "Entrez votre nom complet",
      phonePlaceholder: "Entrez votre numéro de téléphone",
      locationPlaceholder: "Entrez votre emplacement",
    },
    ar: {
      title: "إنشاء حساب",
      subtitle: "انضم إلى منصة جمع جلود الأضاحي",
      email: "عنوان البريد الإلكتروني",
      password: "كلمة المرور",
      confirmPassword: "تأكيد كلمة المرور",
      fullName: "الاسم الكامل",
      role: "أنا",
      donor: "متبرع (لدي جلود للتبرع)",
      collector: "جامع (أجمع الجلود)",
      phone: "رقم الهاتف (اختياري)",
      location: "الموقع",
      signUp: "إنشاء حساب",
      haveAccount: "لديك حساب بالفعل؟",
      signIn: "تسجيل الدخول",
      backToHome: "العودة للرئيسية",
      creating: "جاري إنشاء الحساب...",
      passwordMismatch: "كلمات المرور غير متطابقة",
      weakPassword: "يجب أن تكون كلمة المرور 6 أحرف على الأقل",
      emailExists: "يوجد حساب بهذا البريد الإلكتروني بالفعل",
      networkError: "خطأ في الشبكة. يرجى المحاولة مرة أخرى.",
      successMessage:
        "تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني لتأكيد حسابك.",
      emailPlaceholder: "أدخل بريدك الإلكتروني",
      passwordPlaceholder: "أدخل كلمة المرور",
      confirmPasswordPlaceholder: "أكد كلمة المرور",
      fullNamePlaceholder: "أدخل اسمك الكامل",
      phonePlaceholder: "أدخل رقم هاتفك",
      locationPlaceholder: "أدخل موقعك",
    },
  };

  const t = (key) => translations[language][key] || key;

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    setLoading(true);
    setError("");

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError(t("passwordMismatch"));
      setLoading(false);
      return;
    }

    if (formData.password.length < 6) {
      setError(t("weakPassword"));
      setLoading(false);
      return;
    }

    const { error: signUpError } = await signUp(
      formData.email,
      formData.password,
      {
        full_name: formData.fullName,
        role: formData.role,
        phone: formData.phone,
        location: formData.location,
      }
    );

    if (signUpError) {
      if (signUpError.message.includes("already registered")) {
        setError(t("emailExists"));
      } else {
        setError(t("networkError"));
      }
      setLoading(false);
    } else {
      setSuccess(true);
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div
        className={`min-h-screen bg-gradient-to-br from-amber-50 to-orange-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${
          language === "ar" ? "rtl" : "ltr"
        }`}
      >
        <div className="max-w-md w-full">
          <div className="bg-white p-8 rounded-xl shadow-lg text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-8 h-8 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Success!</h2>
            <p className="text-gray-600 mb-6">{t("successMessage")}</p>
            <Link
              href="/login"
              className="bg-amber-600 hover:bg-amber-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
            >
              {t("signIn")}
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-amber-50 to-orange-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${
        language === "ar" ? "rtl" : "ltr"
      }`}
    >
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="w-16 h-16 bg-amber-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
              />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-gray-900">{t("title")}</h2>
          <p className="mt-2 text-sm text-gray-600">{t("subtitle")}</p>
        </div>

        {/* Language Selector */}
        <div className="flex justify-center">
          <select
            value={language}
            onChange={(e) => setLanguage(e.target.value)}
            className="bg-white border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500"
            aria-label="Select language"
          >
            <option value="en">English</option>
            <option value="fr">Français</option>
            <option value="ar">العربية</option>
          </select>
        </div>

        {/* Registration Form */}
        <form
          className="mt-8 space-y-6 bg-white p-8 rounded-xl shadow-lg"
          onSubmit={handleSubmit}
        >
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
              {error}
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label
                htmlFor="fullName"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                {t("fullName")}
              </label>
              <input
                id="fullName"
                name="fullName"
                type="text"
                required
                value={formData.fullName}
                onChange={handleChange}
                className="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                placeholder={t("fullNamePlaceholder")}
              />
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                {t("email")}
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                placeholder={t("emailPlaceholder")}
              />
            </div>

            <div>
              <label
                htmlFor="role"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                {t("role")}
              </label>
              <select
                id="role"
                name="role"
                value={formData.role}
                onChange={handleChange}
                className="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              >
                <option value="DONOR">{t("donor")}</option>
                <option value="COLLECTOR">{t("collector")}</option>
              </select>
            </div>

            <div>
              <label
                htmlFor="location"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                {t("location")}
              </label>
              <input
                id="location"
                name="location"
                type="text"
                required
                value={formData.location}
                onChange={handleChange}
                className="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                placeholder={t("locationPlaceholder")}
              />
            </div>

            <div>
              <label
                htmlFor="phone"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                {t("phone")}
              </label>
              <input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleChange}
                className="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                placeholder={t("phonePlaceholder")}
              />
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                {t("password")}
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="new-password"
                required
                value={formData.password}
                onChange={handleChange}
                className="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                placeholder={t("passwordPlaceholder")}
              />
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                {t("confirmPassword")}
              </label>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                autoComplete="new-password"
                required
                value={formData.confirmPassword}
                onChange={handleChange}
                className="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                placeholder={t("confirmPasswordPlaceholder")}
              />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-amber-600 hover:bg-amber-700 disabled:bg-amber-400 text-white py-3 px-4 rounded-lg font-semibold transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2"
            >
              {loading ? t("creating") : t("signUp")}
            </button>
          </div>

          <div className="text-center space-y-2">
            <p className="text-sm text-gray-600">
              {t("haveAccount")}{" "}
              <Link
                href="/login"
                className="text-amber-600 hover:text-amber-700 font-medium"
              >
                {t("signIn")}
              </Link>
            </p>
            <Link
              href="/"
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              {t("backToHome")}
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}
