# Docker Deployment Guide for Sheepskin App

This guide will help you deploy your Sheepskin app using Docker on EC2.

## Prerequisites

- EC2 instance (Amazon Linux 2 or Ubuntu)
- SSH access to your EC2 instance
- Your repository access

## Quick Deployment (Automated)

1. **Connect to your EC2 instance:**

   ```bash
   ssh -i your-key.pem ec2-user@your-ec2-ip
   # or for Ubuntu: ssh -i your-key.pem ubuntu@your-ec2-ip
   ```

2. **Run the automated deployment script:**
   ```bash
   curl -fsSL https://raw.githubusercontent.com/khairiEsprit/sheepskin/main/scripts/docker-deploy.sh | bash
   ```

## Manual Deployment

### Step 1: Install Docker

```bash
# Download and run the Docker installation script
curl -fsSL https://raw.githubusercontent.com/khairiEsprit/sheepskin/main/scripts/install-docker.sh | bash

# Or install manually:
# For Amazon Linux 2:
sudo yum update -y
sudo yum install -y docker
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -a -G docker ec2-user

# For Ubuntu:
sudo apt-get update
sudo apt-get install -y docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -a -G docker ubuntu

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### Step 2: Clone Your Repository

```bash
# Create application directory
sudo mkdir -p /opt/sheepskin-app
sudo chown $USER:$USER /opt/sheepskin-app
cd /opt/sheepskin-app

# Clone your repository
git clone https://github.com/khairiEsprit/sheepskin.git .
```

### Step 3: Configure Environment

```bash
# Copy environment template
cp .env.docker.example .env

# Edit the environment file
nano .env
```

**Required environment variables:**

```env
# Database
DB_PASSWORD=your_secure_database_password

# NextAuth
NEXTAUTH_URL=http://your-domain.com  # or http://your-ec2-ip:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Database URL
DATABASE_URL=*****************************************************************/sheepskin
```

**Generate secure values:**

```bash
# Generate secure password
openssl rand -base64 32

# Generate NextAuth secret
openssl rand -base64 32
```

### Step 4: Deploy the Application

```bash
# Build and start the services
docker-compose up -d

# Check if services are running
docker-compose ps

# View logs
docker-compose logs -f
```

### Step 5: Run Database Migrations

```bash
# Run Prisma migrations
docker-compose exec app npx prisma migrate deploy

# Seed the database (optional)
docker-compose exec app npm run db:seed
```

## Useful Docker Commands

### Application Management

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart services
docker-compose restart

# View logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f app
docker-compose logs -f postgres

# Update application
git pull origin main
docker-compose build --no-cache
docker-compose up -d
```

### Database Management

```bash
# Access database
docker-compose exec postgres psql -U postgres -d sheepskin

# Backup database
docker-compose exec postgres pg_dump -U postgres sheepskin > backup.sql

# Restore database
docker-compose exec -T postgres psql -U postgres sheepskin < backup.sql

# Reset database
docker-compose exec app npx prisma migrate reset --force
```

### Container Management

```bash
# Access app container
docker-compose exec app sh

# Access database container
docker-compose exec postgres bash

# View container stats
docker stats

# Clean up unused images
docker system prune -a
```

## Production Optimizations

### 1. Use Environment-Specific Configuration

Create different compose files for different environments:

```bash
# Development
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# Production
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### 2. Set Up SSL/TLS with Let's Encrypt

```bash
# Install Certbot
sudo apt-get install -y certbot

# Get SSL certificate
sudo certbot certonly --standalone -d your-domain.com

# Update your compose file to include SSL
```

### 3. Set Up Monitoring

```bash
# Add monitoring services to your compose file
# - Prometheus
# - Grafana
# - Node Exporter
```

### 4. Backup Strategy

```bash
# Create backup script
cat > /opt/sheepskin-app/backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec -T postgres pg_dump -U postgres sheepskin > /opt/backups/sheepskin_$DATE.sql
find /opt/backups -name "sheepskin_*.sql" -mtime +7 -delete
EOF

chmod +x /opt/sheepskin-app/backup.sh

# Add to crontab
echo "0 2 * * * /opt/sheepskin-app/backup.sh" | crontab -
```

## Troubleshooting

### Common Issues

1. **Permission denied for Docker:**

   ```bash
   # Add user to docker group and relogin
   sudo usermod -a -G docker $USER
   newgrp docker
   ```

2. **Port already in use:**

   ```bash
   # Check what's using the port
   sudo netstat -tulpn | grep :3000
   # Kill the process or change the port in docker-compose.yml
   ```

3. **Database connection issues:**

   ```bash
   # Check if database is running
   docker-compose logs postgres
   # Verify environment variables
   docker-compose exec app env | grep DATABASE_URL
   ```

4. **Prisma schema not found:**

   ```bash
   # If you get "Could not find Prisma Schema" error, rebuild the containers
   docker-compose down
   docker-compose build --no-cache
   docker-compose up -d
   # Then run migrations
   docker-compose exec app npx prisma migrate deploy
   ```

5. **Build failures:**
   ```bash
   # Clear build cache
   docker-compose build --no-cache
   # Check build logs
   docker-compose logs app
   ```

### **rebuild the container**

```bash

docker-compose down

# Rebuild the containers with the updated Dockerfile
docker-compose build --no-cache

# Start the containers again
docker-compose up -d


```

```bash
# Check if app is responding
curl http://localhost:3000

# Check database connectivity
docker-compose exec app npx prisma db push --preview-feature

# Monitor resource usage
docker stats
```

## Security Considerations

1. **Environment Variables:** Never commit sensitive data to git
2. **Database:** Use strong passwords and consider encryption at rest
3. **Network:** Use Docker networks and limit exposed ports
4. **Updates:** Regularly update base images and dependencies
5. **Backup:** Implement automated backup and recovery procedures

## Support

If you encounter any issues:

1. Check the logs: `docker-compose logs -f`
2. Verify your environment configuration
3. Ensure all required ports are open
4. Check Docker and Docker Compose versions
5. Review the troubleshooting section above

For additional help, refer to:

- [Docker Documentation](https://docs.docker.com/)
- [Next.js Deployment Documentation](https://nextjs.org/docs/deployment)
- [Prisma Deployment Guide](https://www.prisma.io/docs/guides/deployment)
