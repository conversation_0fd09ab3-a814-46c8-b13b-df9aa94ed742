import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  console.log("Seeding database...");

  // Create test users
  const donor1 = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      name: "<PERSON>",
      email: "<EMAIL>",
      password: "password123", // In production, this should be hashed
      role: "DON<PERSON>",
      contact: "+216 20 123 456",
      location: "Tunis, Tunisia",
    },
  });

  const donor2 = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      password: "password123", // In production, this should be hashed
      role: "DONOR",
      contact: "+216 22 654 321",
      location: "Sfax, Tunisia",
    },
  });

  const collector1 = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      name: "<PERSON>",
      email: "<EMAIL>",
      password: "password123", // In production, this should be hashed
      role: "COLLECTOR",
      contact: "+216 25 789 123",
      location: "Ariana, Tunisia",
    },
  });

  // Create sample donations
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(14, 0, 0, 0);

  const nextWeek = new Date();
  nextWeek.setDate(nextWeek.getDate() + 7);
  nextWeek.setHours(10, 0, 0, 0);

  await prisma.donation.createMany({
    data: [
      {
        donorId: donor1.id,
        numberOfHides: 3,
        location: "Tunis, Bab Bhar",
        status: "AVAILABLE",
        collectionTime: tomorrow,
      },
      {
        donorId: donor1.id,
        numberOfHides: 2,
        location: "Tunis, Medina",
        status: "COLLECTED",
        collectionTime: new Date("2024-06-19T14:30:00"),
      },
      {
        donorId: donor2.id,
        numberOfHides: 5,
        location: "Sfax, Centre Ville",
        status: "CLAIMED",
        collectionTime: nextWeek,
      },
    ],
  });

  console.log("Database seeded successfully!");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
