import NextAuth, { NextAuthOptions } from "next-auth";
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials";
import { prisma } from "@/lib/prisma";

export const authOptions: NextAuthOptions = {
  // NextAuth.js configuration
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        // Input validation
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        try {
          // Find user in database
          const user = await prisma.user.findUnique({
            where: {
              email: credentials.email.toLowerCase(),
            },
          });

          if (!user) {
            throw new Error("Invalid email or password");
          }

          // For now, compare plain text passwords
          // TODO: Use bcrypt.compare(credentials.password, user.password) when passwords are hashed
          const isValidPassword = credentials.password === user.password;

          if (!isValidPassword) {
            throw new Error("Invalid email or password");
          }

          // Return user object (excluding password)
          return {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            contact: user.contact ?? undefined,
            location: user.location ?? undefined,
          };
        } catch (error) {
          console.error("Authentication error:", error);
          throw new Error("Authentication failed");
        }
      },
    }),
  ],

  // Session configuration
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },

  // JWT configuration
  callbacks: {
    // Include role in JWT token
    async jwt({ token, user }: any) {
      if (user) {
        token.role = user.role;
        token.contact = user.contact;
        token.location = user.location;
      }
      return token;
    },

    // Include role in session
    async session({ session, token }: any) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
        session.user.contact = token.contact as string;
        session.user.location = token.location as string;
      }
      return session;
    },
  },
  // Custom pages
  pages: {
    signIn: "/login",
  },

  // Error handling
  debug: process.env.NODE_ENV === "development",
};
