#!/bin/bash

# She<PERSON>skin Collection Platform - EC2 Setup Script
# This script sets up the initial deployment environment on EC2

set -e

echo "🚀 Setting up Sheepskin Collection Platform on EC2..."

# Update system
echo "📦 Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
echo "📦 Installing Node.js 18..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PostgreSQL
echo "📦 Installing PostgreSQL..."
sudo apt install postgresql postgresql-contrib -y
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Setup PostgreSQL database
echo "🗄️ Setting up PostgreSQL database..."
sudo -u postgres psql << EOF
CREATE DATABASE sheepskin_db;
CREATE USER sheepskin_user WITH ENCRYPTED PASSWORD 'change_this_password_in_production';
GRANT ALL PRIVILEGES ON DATABASE sheepskin_db TO sheepskin_user;
ALTER USER sheepskin_user CREATEDB;
\q
EOF

# Install PM2
echo "📦 Installing PM2..."
sudo npm install -g pm2

# Install Nginx
echo "📦 Installing Nginx..."
sudo apt install nginx -y
sudo systemctl start nginx
sudo systemctl enable nginx

# Create application directory
echo "📁 Creating application directory..."
sudo mkdir -p /home/<USER>/sheepskin-app
sudo chown ubuntu:ubuntu /home/<USER>/sheepskin-app

# Clone repository (you'll need to update this URL)
echo "📥 Cloning repository..."
cd /home/<USER>
git clone https://github.com/khairiEsprit/sheepskin.git
cd sheepskin-app

# Install dependencies
echo "📦 Installing application dependencies..."
npm install

# Create production environment file
echo "⚙️ Creating environment configuration..."
cat > .env.production << EOF
DATABASE_URL="postgresql://sheepskin_user:change_this_password_in_production@localhost:5432/sheepskin_db"
NEXTAUTH_SECRET="$(openssl rand -base64 32)"
NEXTAUTH_URL="http://$(curl -s http://***************/latest/meta-data/public-ipv4)"
NODE_ENV="production"
EOF

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run database migrations
echo "🗄️ Running database migrations..."
npx prisma migrate deploy

# Seed database
echo "🌱 Seeding database..."
npm run db:seed

# Build application
echo "🏗️ Building application..."
npm run build

# Configure Nginx
echo "⚙️ Configuring Nginx..."
sudo tee /etc/nginx/sites-available/sheepskin-app << EOF
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Enable site
sudo ln -sf /etc/nginx/sites-available/sheepskin-app /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl restart nginx

# Start application with PM2
echo "🚀 Starting application..."
pm2 start npm --name "sheepskin-app" -- start
pm2 save
pm2 startup

# Configure firewall
echo "🔒 Configuring firewall..."
sudo ufw --force enable
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow 3000

echo "✅ Setup complete!"
echo ""
echo "🎉 Sheepskin Collection Platform is now running!"
echo "📍 Access your app at: http://$(curl -s http://***************/latest/meta-data/public-ipv4)"
echo ""
echo "🔧 Next steps:"
echo "1. Update the NEXTAUTH_URL in .env.production if you have a custom domain"
echo "2. Update the database password in .env.production"
echo "3. Configure SSL certificate for production use"
echo "4. Set up monitoring and backups"
