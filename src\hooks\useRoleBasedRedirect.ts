"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

/**
 * Custom hook to handle role-based redirection
 * @param {boolean} immediate - Whether to redirect immediately when user is detected
 * @returns {function} redirectBasedOnRole - Function to manually trigger role-based redirect
 */
export function useRoleBasedRedirect(immediate = false) {
  const { user, loading } = useAuth();
  const router = useRouter();

  const redirectBasedOnRole = (userRole = user?.role) => {
    if (!userRole) return;

    switch (userRole) {
      case "DONOR":
        router.replace("/donor-dashboard");
        break;
      case "COLLECTOR":
        router.replace("/collector-dashboard");
        break;
      case "ADMIN":
        router.replace("/admin-dashboard");
        break;
      default:
        // Unknown role, redirect to home
        router.replace("/");
        break;
    }
  };

  useEffect(() => {
    if (immediate && !loading && user) {
      redirectBasedOnRole();
    }
  }, [user, loading, immediate]);

  return redirectBasedOnRole;
}
