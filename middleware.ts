import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

export default withAuth(
  function middleware(req) {
    // Temporarily disable role-based redirects to debug the infinite loop
    // The ProtectedRoute components will handle role-based access control
    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const pathname = req.nextUrl.pathname;

        // Public routes that don't require authentication
        const publicRoutes = [
          "/",
          "/login",
          "/register",
          "/about",
          "/contact",
          "/api/auth/register",
        ];

        // Allow access to public routes
        if (publicRoutes.some((route) => pathname.startsWith(route))) {
          return true;
        }

        // Allow access to NextAuth API routes
        if (pathname.startsWith("/api/auth")) {
          return true;
        }

        // Require authentication for protected routes
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     */
    "/((?!_next/static|_next/image|favicon.ico|public).*)",
  ],
};
