"use client";

import { useState, useEffect } from "react";
import ProtectedRoute from "@/components/ProtectedRoute";
import { useAuth } from "@/contexts/AuthContext";
// Import OpenStreetMap Geo utilities and MapLibre GL JS
import { getMapConfig } from "@/lib/geo";
import maplibregl from "maplibre-gl";
import "maplibre-gl/dist/maplibre-gl.css";

export default function CollectorDashboard() {
  const [language, setLanguage] = useState("en");
  const [activeTab, setActiveTab] = useState("dashboard");
  const [collections, setCollections] = useState([]);
  const [donations, setDonations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [donationsLoading, setDonationsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [donationsError, setDonationsError] = useState(null);
  const [completing, setCompleting] = useState(null);
  const [claiming, setClaiming] = useState(null);
  const [selectedDonation, setSelectedDonation] = useState(null);
  const [showClaimModal, setShowClaimModal] = useState(false);
  // Map related states
  const [mapInitializing, setMapInitializing] = useState(false);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [mapError, setMapError] = useState(null);

  // Fetch collections from API
  useEffect(() => {
    fetchCollections();
    fetchAvailableDonations();
  }, []);

  // Initialize map when donations tab is active
  useEffect(() => {
    if (activeTab === "donationsMap") {
      // Add a small delay to ensure the DOM is ready
      setTimeout(() => {
        initializeMap();
      }, 100);
    } else {
      // Clean up map when leaving the donations map tab
      if (window.dashboardMap) {
        console.log("Cleaning up dashboard map on tab change...");
        window.dashboardMap.remove();
        window.dashboardMap = null;
        setMapLoaded(false);
        setMapInitializing(false);
        setMapError(null);
      }
    }
  }, [activeTab]);

  // Update markers when donations or collections change and map is loaded
  useEffect(() => {
    if (
      mapLoaded &&
      window.dashboardMap &&
      activeTab === "donationsMap" &&
      (donations.length > 0 || collections.length > 0)
    ) {
      console.log("Updating markers due to data change");
      addDashboardMarkers(window.dashboardMap);
    }
  }, [donations, collections, mapLoaded, activeTab]);

  const fetchCollections = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch("/api/collections");

      if (!response.ok) {
        throw new Error("Failed to fetch collections");
      }

      const data = await response.json();
      console.log("Fetched collections:", data);
      setCollections(data);
    } catch (err) {
      console.error("Error fetching collections:", err);
      setError("Failed to load collections. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableDonations = async () => {
    try {
      setDonationsLoading(true);
      setDonationsError(null);
      const response = await fetch("/api/donations/available");

      if (!response.ok) {
        throw new Error("Failed to fetch donations");
      }

      const data = await response.json();
      console.log("Fetched donations:", data);
      setDonations(data);
    } catch (err) {
      console.error("Error fetching donations:", err);
      setDonationsError("Failed to load donations. Please try again.");
    } finally {
      setDonationsLoading(false);
    }
  };

  const translations = {
    en: {
      dashboard: "Dashboard",
      availableDonations: "Available Donations",
      donationsMap: "Donations Map",
      myCollections: "My Collections",
      profile: "Profile",
      collectorDashboard: "Collector Dashboard",
      totalCollections: "Total Collections",
      totalDonations: "Available Donations",
      activeCollections: "Active Collections",
      completedCollections: "Completed Collections",
      id: "ID",
      donationId: "Donation ID",
      numberOfHides: "Number of Hides",
      location: "Location",
      status: "Status",
      pickupTime: "Pickup Time",
      donor: "Donor",
      actions: "Actions",
      markCompleted: "Mark Completed",
      claim: "Claim",
      claimDonation: "Claim Donation",
      confirmClaim: "Confirm Claim",
      confirmMessage: "Are you sure you want to claim this donation?",
      confirm: "Confirm",
      cancel: "Cancel",
      claimed: "Claimed",
      completed: "Completed",
      inProgress: "In Progress",
      available: "Available",
      pending: "Pending",
      collected: "Collected",
      logout: "Logout",
    },
    fr: {
      dashboard: "Tableau de Bord",
      availableDonations: "Dons Disponibles",
      donationsMap: "Carte des Dons",
      myCollections: "Mes Collections",
      profile: "Profil",
      collectorDashboard: "Tableau de Bord Collecteur",
      totalCollections: "Total des Collections",
      totalDonations: "Dons Disponibles",
      activeCollections: "Collections Actives",
      completedCollections: "Collections Terminées",
      id: "ID",
      donationId: "ID du Don",
      numberOfHides: "Nombre de Peaux",
      location: "Emplacement",
      status: "Statut",
      pickupTime: "Heure de Ramassage",
      donor: "Donateur",
      actions: "Actions",
      markCompleted: "Marquer Terminé",
      claim: "Réclamer",
      claimDonation: "Réclamer le Don",
      confirmClaim: "Confirmer la Réclamation",
      confirmMessage: "Êtes-vous sûr de vouloir réclamer ce don?",
      confirm: "Confirmer",
      cancel: "Annuler",
      claimed: "Réclamé",
      completed: "Terminé",
      inProgress: "En Cours",
      available: "Disponible",
      pending: "En Attente",
      collected: "Collecté",
      logout: "Se déconnecter",
    },
    ar: {
      dashboard: "لوحة التحكم",
      availableDonations: "التبرعات المتاحة",
      donationsMap: "خريطة التبرعات",
      myCollections: "مجموعاتي",
      profile: "الملف الشخصي",
      collectorDashboard: "لوحة تحكم الجامع",
      totalCollections: "إجمالي المجموعات",
      totalDonations: "التبرعات المتاحة",
      activeCollections: "المجموعات النشطة",
      completedCollections: "المجموعات المكتملة",
      id: "المعرف",
      donationId: "معرف التبرع",
      numberOfHides: "عدد الجلود",
      location: "الموقع",
      status: "الحالة",
      pickupTime: "وقت الاستلام",
      donor: "المتبرع",
      actions: "الإجراءات",
      markCompleted: "وضع علامة مكتمل",
      claim: "مطالبة",
      claimDonation: "المطالبة بالتبرع",
      confirmClaim: "تأكيد المطالبة",
      confirmMessage: "هل أنت متأكد من أنك تريد المطالبة بهذا التبرع؟",
      confirm: "تأكيد",
      cancel: "إلغاء",
      claimed: "مطالب به",
      completed: "مكتمل",
      inProgress: "قيد التنفيذ",
      available: "متاح",
      pending: "في الانتظار",
      collected: "تم الجمع",
      logout: "تسجيل الخروج",
    },
  };

  const t = (key) => translations[language][key] || key;

  const markCompleted = async (collectionId) => {
    try {
      setCompleting(collectionId);
      const response = await fetch(`/api/collections/${collectionId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: "COMPLETED",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Failed to mark collection as completed"
        );
      }

      // Refresh collections list
      await fetchCollections();

      // Show success message
      alert("Collection marked as completed successfully!");
    } catch (err) {
      console.error("Error marking collection as completed:", err);
      alert(
        err.message ||
          "Failed to mark collection as completed. Please try again."
      );
    } finally {
      setCompleting(null);
    }
  };

  const stats = {
    total: collections.length,
    active: collections.filter((c) => c.status === "PENDING").length,
    completed: collections.filter((c) => c.status === "COMPLETED").length,
    availableDonations: donations.filter((d) => d.status === "AVAILABLE")
      .length,
  };

  const { user, profile, signOut } = useAuth();

  const initializeMap = async () => {
    console.log("Dashboard map initialization started");

    // Clean up existing map if it exists
    if (window.dashboardMap) {
      console.log("Cleaning up existing dashboard map...");
      window.dashboardMap.remove();
      window.dashboardMap = null;
    }

    // Check if container exists
    const container = document.getElementById("dashboard-map");
    if (!container) {
      console.error("Dashboard map container not found!");
      setMapError("Map container not found");
      return;
    }

    try {
      setMapInitializing(true);
      setMapError(null);
      setMapLoaded(false);
      console.log("Getting map configuration for dashboard...");

      // Use OpenStreetMap configuration
      const mapStyle = getMapConfig();

      console.log("Creating MapLibre GL map for dashboard...");
      const map = new maplibregl.Map({
        container: "dashboard-map",
        style: mapStyle,
        center: [10.1815, 36.8065], // Center on Tunisia (Tunis)
        zoom: 8,
      });

      console.log("Dashboard map created, adding controls...");
      map.addControl(new maplibregl.NavigationControl(), "top-left");

      map.on("load", () => {
        console.log("Dashboard map loaded successfully!");
        // Hide fallback content
        const fallback = document.getElementById("dashboard-map-fallback");
        if (fallback) {
          fallback.style.display = "none";
          console.log("Dashboard map fallback content hidden");
        }

        setMapLoaded(true);
        setMapInitializing(false);

        // Add donation markers to the map
        console.log("Adding dashboard markers...");
        addDashboardMarkers(map);
      });

      map.on("error", (e) => {
        console.error("Dashboard map error:", e);
        setMapError("Failed to load map. Please refresh the page.");
        setMapInitializing(false);
      });

      // Store map instance
      window.dashboardMap = map;
    } catch (error) {
      console.error("Error setting up dashboard map:", error);
      setMapError(`Failed to initialize map: ${error.message}`);
      setMapInitializing(false);
    }
  };

  const addDashboardMarkers = (map) => {
    console.log("=== Adding Dashboard Markers ===");
    console.log("Donations:", donations);
    console.log("Collections:", collections);

    // Clear existing markers
    if (window.dashboardMarkers) {
      window.dashboardMarkers.forEach((marker) => marker.remove());
    }
    window.dashboardMarkers = [];

    // Add markers for both donations and collections
    const allItems = [
      ...donations,
      ...collections.map((c) => ({
        ...c.donation,
        isCollection: true,
        collectionStatus: c.status,
      })),
    ];

    console.log("All items for markers:", allItems);

    allItems.forEach((item) => {
      if (item.latitude && item.longitude) {
        console.log(
          "Adding marker for item:",
          item.id,
          "at",
          item.latitude,
          item.longitude
        );
        // Create marker element
        const markerElement = document.createElement("div");
        const isCollection = item.isCollection;
        const status = isCollection ? item.collectionStatus : item.status;

        let backgroundColor;
        if (isCollection) {
          backgroundColor = status === "COMPLETED" ? "#059669" : "#dc2626"; // Green for completed, red for pending
        } else {
          backgroundColor = status === "AVAILABLE" ? "#10b981" : "#f59e0b"; // Green for available, orange for claimed
        }

        markerElement.style.cssText = `
          width: 35px;
          height: 35px;
          border-radius: 50%;
          background-color: ${backgroundColor};
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 12px;
          cursor: pointer;
          border: 3px solid white;
          box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        `;
        markerElement.textContent = isCollection ? "C" : item.numberOfHides;

        // Create popup
        const popup = new maplibregl.Popup({ offset: 25 }).setHTML(`
          <div class="p-3">
            <h3 class="font-bold mb-2">${
              isCollection ? "Collection" : "Donation"
            } #${item.id.slice(-8)}</h3>
            <p><strong>Donor:</strong> ${item.donor?.name || "Unknown"}</p>
            <p><strong>Location:</strong> ${item.location}</p>
            <p><strong>Hides:</strong> ${item.numberOfHides}</p>
            <p><strong>Status:</strong> ${status}</p>
            ${
              !isCollection && item.status === "AVAILABLE"
                ? `<button onclick="window.claimFromDashboard('${item.id}')" class="mt-2 bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">Claim</button>`
                : ""
            }
          </div>
        `);

        // Add marker to map
        const marker = new maplibregl.Marker(markerElement)
          .setLngLat([item.longitude, item.latitude])
          .setPopup(popup)
          .addTo(map);

        window.dashboardMarkers.push(marker);

        // Add click handler for donations
        if (!isCollection && item.status === "AVAILABLE") {
          markerElement.addEventListener("click", () => {
            handleClaim(item);
          });
        }
      } else {
        console.log(
          "Item missing coordinates:",
          item.id,
          "lat:",
          item.latitude,
          "lng:",
          item.longitude
        );
      }
    });

    console.log("Total markers added:", window.dashboardMarkers.length);
  };

  const handleClaim = (donation) => {
    setSelectedDonation(donation);
    setShowClaimModal(true);
  };

  const confirmClaim = async () => {
    if (!selectedDonation) return;

    try {
      setClaiming(selectedDonation.id);
      const response = await fetch("/api/collections", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          donationId: selectedDonation.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to claim donation");
      }

      // Refresh both collections and donations
      await Promise.all([fetchCollections(), fetchAvailableDonations()]);

      alert("Donation claimed successfully!");
    } catch (err) {
      console.error("Error claiming donation:", err);
      alert(err.message || "Failed to claim donation. Please try again.");
    } finally {
      setClaiming(null);
      setShowClaimModal(false);
      setSelectedDonation(null);
    }
  };

  // Expose claim function globally for popup buttons
  useEffect(() => {
    window.claimFromDashboard = (donationId) => {
      const donation = donations.find((d) => d.id === donationId);
      if (donation && donation.status === "AVAILABLE") {
        handleClaim(donation);
      }
    };

    return () => {
      if (window.claimFromDashboard) {
        window.claimFromDashboard = null;
      }
      if (window.dashboardMap) {
        console.log("Cleaning up dashboard map...");
        window.dashboardMap.remove();
        window.dashboardMap = null;
      }
      if (window.dashboardMarkers) {
        window.dashboardMarkers.forEach((marker) => marker.remove());
        window.dashboardMarkers = null;
      }
    };
  }, [donations]);

  return (
    <ProtectedRoute requiredRole="COLLECTOR">
      <div
        className={`min-h-screen bg-gray-50 ${
          language === "ar" ? "rtl" : "ltr"
        }`}
      >
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white shadow-lg min-h-screen">
            <div className="p-6">
              <h1 className="text-2xl font-bold text-gray-900">
                {t("collectorDashboard")}
              </h1>

              {/* Language Selector */}
              <div className="mt-4">
                <select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  className="w-full bg-white border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  aria-label="Select language"
                >
                  <option value="en">English</option>
                  <option value="fr">Français</option>
                  <option value="ar">العربية</option>
                </select>
              </div>
            </div>

            <nav className="mt-6">
              {[
                "dashboard",
                "availableDonations",
                "donationsMap",
                "myCollections",
                "profile",
              ].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`w-full text-left px-6 py-3 text-sm font-medium transition-colors ${
                    activeTab === tab
                      ? "bg-blue-50 text-blue-700 border-r-2 border-blue-500"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                  aria-label={t(tab)}
                >
                  {t(tab)}
                </button>
              ))}
              <button
                onClick={signOut}
                className="w-full text-left px-6 py-3 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                aria-label={t("logout")}
              >
                {t("logout")}
              </button>
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-8">
            <div className="flex justify-between items-center mb-4">
              <div>
                {activeTab === "dashboard" && (
                  <h2 className="text-3xl font-bold text-gray-900">
                    {t("dashboard")}
                  </h2>
                )}
                {activeTab === "availableDonations" && (
                  <h2 className="text-3xl font-bold text-gray-900">
                    {t("availableDonations")}
                  </h2>
                )}
                {activeTab === "myCollections" && (
                  <h2 className="text-3xl font-bold text-gray-900">
                    {t("myCollections")}
                  </h2>
                )}
                {activeTab === "profile" && (
                  <h2 className="text-3xl font-bold text-gray-900">
                    {t("profile")}
                  </h2>
                )}
              </div>
              {user && profile && (
                <div className="flex items-center space-x-4">
                  <img
                    src={profile.avatar || "/placeholder.svg"}
                    alt="Avatar"
                    className="w-8 h-8 rounded-full"
                  />
                  <div>
                    <p className="text-sm font-semibold text-gray-700">
                      {user.email}
                    </p>
                    <p className="text-xs text-gray-500">
                      {profile.firstName} {profile.lastName}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {activeTab === "dashboard" && (
              <div>
                {/* Stats Cards */}
                <div className="grid md:grid-cols-4 gap-6 mb-8">
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-700">
                      {t("totalCollections")}
                    </h3>
                    <p className="text-3xl font-bold text-blue-600">
                      {stats.total}
                    </p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-700">
                      {t("activeCollections")}
                    </h3>
                    <p className="text-3xl font-bold text-yellow-600">
                      {stats.active}
                    </p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-700">
                      {t("completedCollections")}
                    </h3>
                    <p className="text-3xl font-bold text-green-600">
                      {stats.completed}
                    </p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-700">
                      {t("totalDonations")}
                    </h3>
                    <p className="text-3xl font-bold text-purple-600">
                      {stats.availableDonations}
                    </p>
                  </div>
                </div>

                {/* Recent Collections */}
                <div className="bg-white rounded-lg shadow">
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      {t("myCollections")}
                    </h3>

                    {loading && (
                      <div className="text-center py-4">
                        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p className="mt-2 text-gray-600">
                          Loading collections...
                        </p>
                      </div>
                    )}

                    {error && (
                      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        {error}
                        <button
                          onClick={fetchCollections}
                          className="ml-2 underline hover:no-underline"
                        >
                          Retry
                        </button>
                      </div>
                    )}

                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left py-3 px-4 font-semibold text-gray-700">
                              {t("id")}
                            </th>
                            <th className="text-left py-3 px-4 font-semibold text-gray-700">
                              {t("numberOfHides")}
                            </th>
                            <th className="text-left py-3 px-4 font-semibold text-gray-700">
                              {t("location")}
                            </th>
                            <th className="text-left py-3 px-4 font-semibold text-gray-700">
                              {t("donor")}
                            </th>
                            <th className="text-left py-3 px-4 font-semibold text-gray-700">
                              {t("status")}
                            </th>
                            <th className="text-left py-3 px-4 font-semibold text-gray-700">
                              {t("actions")}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {!loading && !error && collections.length === 0 && (
                            <tr>
                              <td
                                colSpan="6"
                                className="text-center py-8 text-gray-500"
                              >
                                No collections found
                              </td>
                            </tr>
                          )}

                          {collections.slice(0, 5).map((collection) => (
                            <tr
                              key={collection.id}
                              className="border-b hover:bg-gray-50"
                            >
                              <td className="py-3 px-4">
                                #{collection.id.slice(-8)}
                              </td>
                              <td className="py-3 px-4">
                                {collection.donation?.numberOfHides || "N/A"}
                              </td>
                              <td className="py-3 px-4">
                                {collection.donation?.location || "N/A"}
                              </td>
                              <td className="py-3 px-4">
                                {collection.donation?.donor?.name || "Unknown"}
                              </td>
                              <td className="py-3 px-4">
                                <span
                                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    collection.status === "COMPLETED"
                                      ? "bg-green-100 text-green-800"
                                      : "bg-yellow-100 text-yellow-800"
                                  }`}
                                >
                                  {collection.status === "COMPLETED"
                                    ? t("completed")
                                    : t("claimed")}
                                </span>
                              </td>
                              <td className="py-3 px-4">
                                {collection.status === "PENDING" && (
                                  <button
                                    onClick={() => markCompleted(collection.id)}
                                    disabled={completing === collection.id}
                                    className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white py-1 px-3 rounded text-sm font-medium transition-colors"
                                  >
                                    {completing === collection.id
                                      ? "Completing..."
                                      : t("markCompleted")}
                                  </button>
                                )}
                                {collection.status === "COMPLETED" && (
                                  <span className="text-green-600 text-sm">
                                    ✓ Completed
                                  </span>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "myCollections" && (
              <div>
                <div className="bg-white rounded-lg shadow">
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      All My Collections
                    </h3>

                    {loading && (
                      <div className="text-center py-4">
                        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p className="mt-2 text-gray-600">
                          Loading collections...
                        </p>
                      </div>
                    )}

                    {error && (
                      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        {error}
                        <button
                          onClick={fetchCollections}
                          className="ml-2 underline hover:no-underline"
                        >
                          Retry
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b bg-gray-50">
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            Collection {t("id")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            Donation {t("id")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("numberOfHides")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("location")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("donor")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("status")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            Collection Time
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("actions")}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {!loading && !error && collections.length === 0 && (
                          <tr>
                            <td
                              colSpan="8"
                              className="text-center py-8 text-gray-500"
                            >
                              No collections found
                            </td>
                          </tr>
                        )}

                        {collections.map((collection) => (
                          <tr
                            key={collection.id}
                            className="border-b hover:bg-gray-50"
                          >
                            <td className="py-4 px-6">
                              #{collection.id.slice(-8)}
                            </td>
                            <td className="py-4 px-6">
                              #{collection.donation?.id?.slice(-8) || "N/A"}
                            </td>
                            <td className="py-4 px-6">
                              {collection.donation?.numberOfHides || "N/A"}
                            </td>
                            <td className="py-4 px-6">
                              {collection.donation?.location || "N/A"}
                            </td>
                            <td className="py-4 px-6">
                              {collection.donation?.donor?.name || "Unknown"}
                            </td>
                            <td className="py-4 px-6">
                              <span
                                className={`px-3 py-1 rounded-full text-sm font-medium ${
                                  collection.status === "COMPLETED"
                                    ? "bg-green-100 text-green-800"
                                    : "bg-yellow-100 text-yellow-800"
                                }`}
                              >
                                {collection.status === "COMPLETED"
                                  ? t("completed")
                                  : t("claimed")}
                              </span>
                            </td>
                            <td className="py-4 px-6">
                              {collection.donation?.collectionTime
                                ? new Date(
                                    collection.donation.collectionTime
                                  ).toLocaleString()
                                : "N/A"}
                            </td>
                            <td className="py-4 px-6">
                              {collection.status === "PENDING" && (
                                <button
                                  onClick={() => markCompleted(collection.id)}
                                  disabled={completing === collection.id}
                                  className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors"
                                  aria-label={`${t(
                                    "markCompleted"
                                  )} collection ${collection.id}`}
                                >
                                  {completing === collection.id
                                    ? "Completing..."
                                    : t("markCompleted")}
                                </button>
                              )}
                              {collection.status === "COMPLETED" && (
                                <div className="flex items-center text-green-600">
                                  <svg
                                    className="w-4 h-4 mr-1"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                  <span className="text-sm">Completed</span>
                                </div>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "availableDonations" && (
              <div>
                <div className="bg-white rounded-lg shadow">
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      {t("availableDonations")}
                    </h3>

                    {donationsLoading && (
                      <div className="text-center py-4">
                        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p className="mt-2 text-gray-600">
                          Loading donations...
                        </p>
                      </div>
                    )}

                    {donationsError && (
                      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        {donationsError}
                        <button
                          onClick={fetchAvailableDonations}
                          className="ml-2 underline hover:no-underline"
                        >
                          Retry
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b bg-gray-50">
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("id")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("donor")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("numberOfHides")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("location")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            Collection Time
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("status")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("actions")}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {!donationsLoading &&
                          !donationsError &&
                          donations.length === 0 && (
                            <tr>
                              <td
                                colSpan="7"
                                className="text-center py-8 text-gray-500"
                              >
                                No donations available
                              </td>
                            </tr>
                          )}

                        {donations.map((donation) => (
                          <tr
                            key={donation.id}
                            className="border-b hover:bg-gray-50"
                          >
                            <td className="py-4 px-6">
                              #{donation.id.slice(-8)}
                            </td>
                            <td className="py-4 px-6">
                              {donation.donor?.name || "Unknown"}
                            </td>
                            <td className="py-4 px-6">
                              {donation.numberOfHides}
                            </td>
                            <td className="py-4 px-6">{donation.location}</td>
                            <td className="py-4 px-6">
                              {new Date(
                                donation.collectionTime
                              ).toLocaleString()}
                            </td>
                            <td className="py-4 px-6">
                              <span className="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                {t("available")}
                              </span>
                            </td>
                            <td className="py-4 px-6">
                              <button
                                onClick={() => handleClaim(donation)}
                                disabled={claiming === donation.id}
                                className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors"
                              >
                                {claiming === donation.id
                                  ? "Claiming..."
                                  : t("claim")}
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "donationsMap" && (
              <div>
                <div className="bg-white rounded-lg shadow">
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      {t("donationsMap")}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      View all donations and collections on the map. Green
                      markers are available donations, red markers are your
                      pending collections, and dark green markers are completed
                      collections.
                    </p>
                  </div>

                  <div className="h-96 relative">
                    <div
                      id="dashboard-map"
                      key={`dashboard-map-${activeTab}`}
                      className="w-full h-full"
                    >
                      {/* Map will be rendered here by Amazon Location Services */}
                    </div>

                    {/* Loading fallback */}
                    <div
                      className={`absolute inset-0 flex items-center justify-center bg-gray-100 transition-opacity duration-500 ${
                        mapInitializing || !mapLoaded
                          ? "opacity-100"
                          : "opacity-0 pointer-events-none"
                      }`}
                      id="dashboard-map-fallback"
                    >
                      <div className="text-center">
                        {mapError ? (
                          <div className="text-center">
                            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                              <svg
                                className="w-8 h-8 text-red-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                              </svg>
                            </div>
                            <p className="text-red-600 text-lg font-medium mb-2">
                              Map Error
                            </p>
                            <p className="text-red-500 text-sm mb-4">
                              {mapError}
                            </p>
                            <button
                              onClick={initializeMap}
                              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm"
                            >
                              Retry
                            </button>
                          </div>
                        ) : (
                          <>
                            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                              {mapInitializing ? (
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                              ) : (
                                <svg
                                  className="w-8 h-8 text-blue-600"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                                  />
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M15 11a3 3 0 11-6 0 3 3 0 616 0z"
                                  />
                                </svg>
                              )}
                            </div>
                            <p className="text-gray-600 text-lg">
                              {mapInitializing
                                ? "Initializing Interactive Map..."
                                : "Loading Interactive Map"}
                            </p>
                            <p className="text-gray-500 text-sm">
                              OpenStreetMap Services
                            </p>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Legend */}
                  <div className="p-6 border-t bg-gray-50">
                    <h4 className="font-semibold text-gray-900 mb-3">
                      Map Legend:
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="flex items-center">
                        <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                        <span className="text-sm">Available Donations</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-4 h-4 rounded-full bg-red-600 mr-2"></div>
                        <span className="text-sm">Pending Collections</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-4 h-4 rounded-full bg-green-700 mr-2"></div>
                        <span className="text-sm">Completed Collections</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-4 h-4 rounded-full bg-orange-500 mr-2"></div>
                        <span className="text-sm">Claimed Donations</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "profile" && (
              <div>
                <div className="bg-white rounded-lg shadow p-6">
                  <p className="text-gray-600">
                    Profile settings will be implemented here.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Claim Confirmation Modal */}
      {showClaimModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              {t("confirmClaim")}
            </h3>
            <p className="text-gray-600 mb-6">{t("confirmMessage")}</p>
            {selectedDonation && (
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <p>
                  <strong>{t("donor")}:</strong>{" "}
                  {selectedDonation.donor?.name || "Unknown"}
                </p>
                <p>
                  <strong>{t("location")}:</strong> {selectedDonation.location}
                </p>
                <p>
                  <strong>{t("numberOfHides")}:</strong>{" "}
                  {selectedDonation.numberOfHides} {t("hides")}
                </p>
                <p>
                  <strong>Collection Time:</strong>{" "}
                  {new Date(selectedDonation.collectionTime).toLocaleString()}
                </p>
              </div>
            )}
            <div className="flex space-x-4">
              <button
                onClick={confirmClaim}
                disabled={claiming}
                className="flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white py-2 px-4 rounded-lg font-medium transition-colors"
                aria-label={t("confirm")}
              >
                {claiming ? "Claiming..." : t("confirm")}
              </button>
              <button
                onClick={() => setShowClaimModal(false)}
                className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-lg font-medium transition-colors"
                aria-label={t("cancel")}
              >
                {t("cancel")}
              </button>
            </div>
          </div>
        </div>
      )}
    </ProtectedRoute>
  );
}
