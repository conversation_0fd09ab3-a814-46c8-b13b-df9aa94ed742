import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// DELETE: Delete a specific donation
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the session to verify authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify the user is a donor
    if (session.user.role !== "DONOR") {
      return NextResponse.json(
        { error: "Access denied. Only donors can delete donations." },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const donationId = resolvedParams.id;

    // Check if the donation exists and belongs to the authenticated user
    const donation = await prisma.donation.findUnique({
      where: { id: donationId },
    });

    if (!donation) {
      return NextResponse.json(
        { error: "Donation not found" },
        { status: 404 }
      );
    }

    if (donation.donorId !== session.user.id) {
      return NextResponse.json(
        { error: "Access denied. You can only delete your own donations." },
        { status: 403 }
      );
    }

    // Only allow deletion if the donation is still available
    if (donation.status !== "AVAILABLE") {
      return NextResponse.json(
        {
          error:
            "Cannot delete donation. It has already been claimed or collected.",
        },
        { status: 400 }
      );
    }

    // Delete the donation
    await prisma.donation.delete({
      where: { id: donationId },
    });

    return NextResponse.json(
      { message: "Donation deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting donation:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
