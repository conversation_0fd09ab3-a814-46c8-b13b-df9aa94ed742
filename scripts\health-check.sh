#!/bin/bash

# Health check script for Sheepskin Collection Platform
# This script verifies that the application is running correctly

set -e

APP_URL="http://localhost:3000"
MAX_ATTEMPTS=5
ATTEMPT=1

echo "🏥 Performing health check for Sheepskin Collection Platform..."

while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
    echo "Attempt $ATTEMPT of $MAX_ATTEMPTS..."
    
    # Check if the application responds
    if curl -f -s "$APP_URL" > /dev/null; then
        echo "✅ Application is responding at $APP_URL"
        
        # Check if PM2 process is running
        if pm2 list | grep -q "sheepskin-app.*online"; then
            echo "✅ PM2 process is running"
        else
            echo "❌ PM2 process is not running properly"
            exit 1
        fi
        
        # Check if database connection works
        if cd /home/<USER>/sheepskin-app && npx prisma db pull > /dev/null 2>&1; then
            echo "✅ Database connection is working"
        else
            echo "❌ Database connection failed"
            exit 1
        fi
        
        echo "🎉 All health checks passed!"
        exit 0
    else
        echo "❌ Application is not responding (attempt $ATTEMPT)"
        if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
            echo "🚨 Health check failed after $MAX_ATTEMPTS attempts"
            
            # Show PM2 status for debugging
            echo "PM2 Status:"
            pm2 status
            
            # Show recent logs
            echo "Recent PM2 logs:"
            pm2 logs sheepskin-app --lines 20
            
            exit 1
        fi
        
        echo "Waiting 10 seconds before next attempt..."
        sleep 10
        ATTEMPT=$((ATTEMPT + 1))
    fi
done
