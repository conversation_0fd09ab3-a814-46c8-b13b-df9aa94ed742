{"apps": [{"name": "sheepskin-app", "script": "npm", "args": "start", "cwd": "/home/<USER>/sheepskin-app", "instances": 1, "exec_mode": "fork", "watch": false, "max_memory_restart": "1G", "env": {"NODE_ENV": "production", "PORT": 3000}, "error_file": "/home/<USER>/logs/sheepskin-app-error.log", "out_file": "/home/<USER>/logs/sheepskin-app-out.log", "log_file": "/home/<USER>/logs/sheepskin-app-combined.log", "time": true, "autorestart": true, "max_restarts": 10, "min_uptime": "10s"}]}