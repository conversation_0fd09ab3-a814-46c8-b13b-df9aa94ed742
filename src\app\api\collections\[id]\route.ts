import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// PATCH /api/collections/[id] - Update collection status (mark as completed)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "COLLECTOR") {
      return NextResponse.json(
        { error: "Access denied. Collector role required." },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const collectionId = resolvedParams.id;
    const { status } = await request.json();

    if (!status) {
      return NextResponse.json(
        { error: "Status is required" },
        { status: 400 }
      );
    }

    if (status !== "COMPLETED") {
      return NextResponse.json(
        { error: "Only COMPLETED status is allowed" },
        { status: 400 }
      );
    }

    // Check if collection exists and belongs to the current collector
    const existingCollection = await prisma.collection.findUnique({
      where: { id: collectionId },
      include: {
        donation: true,
      },
    });

    if (!existingCollection) {
      return NextResponse.json(
        { error: "Collection not found" },
        { status: 404 }
      );
    }

    if (existingCollection.collectorId !== session.user.id) {
      return NextResponse.json(
        {
          error: "Access denied. This collection belongs to another collector.",
        },
        { status: 403 }
      );
    }

    if (existingCollection.status === "COMPLETED") {
      return NextResponse.json(
        { error: "Collection is already completed" },
        { status: 400 }
      );
    }

    // Use transaction to update collection and donation status
    const result = await prisma.$transaction(async (tx) => {
      // Update collection status to COMPLETED and set collectedAt timestamp
      const updatedCollection = await tx.collection.update({
        where: { id: collectionId },
        data: {
          status: "COMPLETED",
          collectedAt: new Date(),
        },
        include: {
          donation: {
            include: {
              donor: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  contact: true,
                },
              },
            },
          },
        },
      });

      // Update donation status to COLLECTED
      await tx.donation.update({
        where: { id: existingCollection.donationId },
        data: { status: "COLLECTED" },
      });

      return updatedCollection;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error updating collection:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET /api/collections/[id] - Get specific collection details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (session.user.role !== "COLLECTOR") {
      return NextResponse.json(
        { error: "Access denied. Collector role required." },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const collectionId = resolvedParams.id;

    const collection = await prisma.collection.findUnique({
      where: { id: collectionId },
      include: {
        donation: {
          include: {
            donor: {
              select: {
                id: true,
                name: true,
                email: true,
                contact: true,
              },
            },
          },
        },
      },
    });

    if (!collection) {
      return NextResponse.json(
        { error: "Collection not found" },
        { status: 404 }
      );
    }

    if (collection.collectorId !== session.user.id) {
      return NextResponse.json(
        {
          error: "Access denied. This collection belongs to another collector.",
        },
        { status: 403 }
      );
    }

    return NextResponse.json(collection);
  } catch (error) {
    console.error("Error fetching collection:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
