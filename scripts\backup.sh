#!/bin/bash

# Backup script for Sheepskin Collection Platform
# This script creates backups of the database and application

set -e

BACKUP_DIR="/home/<USER>/backups"
APP_DIR="/home/<USER>/sheepskin-app"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
DB_NAME="sheepskin_db"
DB_USER="sheepskin_user"

echo "💾 Starting backup process for Sheepskin Collection Platform..."

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Database backup
echo "🗄️ Backing up database..."
PGPASSWORD="change_this_password_in_production" pg_dump -h localhost -U "$DB_USER" -d "$DB_NAME" > "$BACKUP_DIR/database_$TIMESTAMP.sql"

# Application backup (excluding node_modules and .next)
echo "📁 Backing up application files..."
cd "$APP_DIR"
tar -czf "$BACKUP_DIR/app_$TIMESTAMP.tar.gz" \
    --exclude=node_modules \
    --exclude=.next \
    --exclude=coverage \
    --exclude=.git \
    .

# Environment backup
echo "⚙️ Backing up environment configuration..."
cp .env.production "$BACKUP_DIR/env_$TIMESTAMP.backup"

# Clean up old backups (keep only last 7 days)
echo "🧹 Cleaning up old backups..."
find "$BACKUP_DIR" -name "database_*.sql" -mtime +7 -delete
find "$BACKUP_DIR" -name "app_*.tar.gz" -mtime +7 -delete
find "$BACKUP_DIR" -name "env_*.backup" -mtime +7 -delete

# List current backups
echo "📋 Current backups:"
ls -lah "$BACKUP_DIR"

echo "✅ Backup completed successfully!"
echo "📍 Backup location: $BACKUP_DIR"
echo "🗄️ Database backup: database_$TIMESTAMP.sql"
echo "📁 Application backup: app_$TIMESTAMP.tar.gz"
