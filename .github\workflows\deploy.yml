name: CI/CD Pipeline for Sheepskin Collection Platform

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

env:
  NODE_VERSION: "18"
  APP_NAME: "sheepskin-app"
  APP_DIRECTORY: "/home/<USER>/sheepskin-app"
  REPO_URL: "https://github.com/khairiEsprit/sheepskin.git"

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: sheepskin_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Set up test environment
        run: |
          cat > .env.test << EOF
          DATABASE_URL="postgresql://postgres:postgres@localhost:5432/sheepskin_test"
          NEXTAUTH_SECRET="test-secret-key-for-ci"
          NEXTAUTH_URL="http://localhost:3000"
          NODE_ENV="test"
          EOF

      - name: Generate Prisma client
        run: npx prisma generate

      - name: Run database migrations
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/sheepskin_test"

      - name: Run TypeScript check
        run: npx tsc --noEmit

      - name: Run tests
        run: npm test
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/sheepskin_test"

      - name: Build app
        run: npm run build
        env:
          DATABASE_URL: "postgresql://postgres:postgres@localhost:5432/sheepskin_test"
          NEXTAUTH_SECRET: "test-secret-key-for-ci"
          NEXTAUTH_URL: "http://localhost:3000"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: next-build
          path: |
            .next/
            package.json
            package-lock.json
            prisma/
            public/
          retention-days: 1

      - name: Run dependency security audit
        run: npm audit --audit-level moderate

  deploy:
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: next-build

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Debug secrets and repository access
        env:
          PAT_TOKEN: ${{ secrets.PAT_TOKEN }}
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_KEY: ${{ secrets.EC2_KEY }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
          NEXTAUTH_URL: ${{ secrets.NEXTAUTH_URL }}
        run: |
          echo "Checking if secrets are set..."
          if [ -n "$PAT_TOKEN" ]; then echo "PAT_TOKEN is set"; else echo "Error: PAT_TOKEN is not set"; exit 1; fi
          if [ -n "$EC2_HOST" ]; then echo "EC2_HOST is set"; else echo "Error: EC2_HOST is not set"; exit 1; fi
          if [ -n "$EC2_USER" ]; then echo "EC2_USER is set"; else echo "Error: EC2_USER is not set"; exit 1; fi
          if [ -n "$EC2_KEY" ]; then echo "EC2_KEY is set (length: ${#EC2_KEY} characters)"; else echo "Error: EC2_KEY is not set"; exit 1; fi
          if [ -n "$DATABASE_URL" ]; then echo "DATABASE_URL is set"; else echo "Error: DATABASE_URL is not set"; exit 1; fi
          if [ -n "$NEXTAUTH_SECRET" ]; then echo "NEXTAUTH_SECRET is set"; else echo "Error: NEXTAUTH_SECRET is not set"; exit 1; fi
          if [ -n "$NEXTAUTH_URL" ]; then echo "NEXTAUTH_URL is set"; else echo "Error: NEXTAUTH_URL is not set"; exit 1; fi

          echo "Testing repository access with PAT_TOKEN..."
          status=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: token $PAT_TOKEN" https://api.github.com/repos/khairiEsprit/sheepskin)
          if [ "$status" -eq 200 ]; then
            echo "Repository access successful (HTTP 200)"
          else
            echo "Repository access failed (HTTP $status)"
            echo "Testing with bearer token format..."
            status2=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer $PAT_TOKEN" https://api.github.com/repos/khairiEsprit/sheepskin)
            if [ "$status2" -eq 200 ]; then
              echo "Repository access successful with Bearer token (HTTP 200)"
            else
              echo "Repository access failed with both token formats (HTTP $status2)"
            fi
          fi

          echo "Testing git clone with PAT_TOKEN..."
          git clone https://$PAT_TOKEN:<EMAIL>/khairiEsprit/sheepskin.git test-clone
          if [ $? -eq 0 ]; then
            echo "Git clone test successful"
            rm -rf test-clone
          else
            echo "Git clone test failed"
          fi

          echo "Repository URL: ${{ env.REPO_URL }}"

      - name: Deploy to EC2
        env:
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_KEY: ${{ secrets.EC2_KEY }}
          PAT_TOKEN: ${{ secrets.PAT_TOKEN }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
          NEXTAUTH_URL: ${{ secrets.NEXTAUTH_URL }}
        run: |
          # Create temporary directory for SSH key
          mkdir -p ~/.ssh
          echo "$EC2_KEY" > ~/.ssh/ec2_key.pem
          chmod 600 ~/.ssh/ec2_key.pem

          # Create deployment script with embedded variables
          cat > ~/deploy_script.sh << 'DEPLOY_EOF'
          #!/bin/bash
          set -e

          echo "Starting deployment of Sheepskin Collection Platform..."

          # Set environment variables (these will be substituted when the script is created)
          DATABASE_URL_VAR="DATABASE_URL_PLACEHOLDER"
          NEXTAUTH_SECRET_VAR="NEXTAUTH_SECRET_PLACEHOLDER"
          NEXTAUTH_URL_VAR="NEXTAUTH_URL_PLACEHOLDER"
          PAT_TOKEN_VAR="PAT_TOKEN_PLACEHOLDER"

          echo "Environment variables loaded:"
          echo "DATABASE_URL length: ${#DATABASE_URL_VAR}"
          echo "NEXTAUTH_SECRET length: ${#NEXTAUTH_SECRET_VAR}"
          echo "NEXTAUTH_URL length: ${#NEXTAUTH_URL_VAR}"

          # Create app directory
          mkdir -p /home/<USER>/sheepskin-app
          cd /home/<USER>/sheepskin-app

          # Create backup of current deployment
          BACKUP_DIR="/tmp/sheepskin-backup-$(date +%Y%m%d_%H%M%S)"
          if [ -d ".next" ]; then
            echo "Creating backup..."
            mkdir -p "$BACKUP_DIR"
            cp -r .next "$BACKUP_DIR/" 2>/dev/null || true
            cp .env "$BACKUP_DIR/" 2>/dev/null || true
          fi

          # Completely remove and recreate deployment directory
          cd /home/<USER>
          sudo rm -rf sheepskin-app
          mkdir -p sheepskin-app
          cd sheepskin-app

          # Clone repository
          echo "Cloning repository..."
          git clone https://$PAT_TOKEN_VAR:<EMAIL>/khairiEsprit/sheepskin.git .

          # Create .env file
          echo "Setting up environment variables..."
          cat > .env << ENV_EOF
          DATABASE_URL="$DATABASE_URL_VAR"
          NEXTAUTH_SECRET="$NEXTAUTH_SECRET_VAR"
          NEXTAUTH_URL="$NEXTAUTH_URL_VAR"
          NODE_ENV="production"
          ENV_EOF

          echo "Contents of .env file:"
          cat .env

          # Verify PostgreSQL is running
          echo "Checking PostgreSQL status..."
          if sudo systemctl is-active --quiet postgresql; then
            echo "PostgreSQL is running"
          else
            echo "Starting PostgreSQL..."
            sudo systemctl start postgresql
            sleep 3
          fi

          # Verify database connection
          echo "Testing database connection..."
          if sudo -u postgres psql -d sheepskin_db -c "SELECT 1;" > /dev/null 2>&1; then
            echo "Database connection successful"
          else
            echo "Setting up database..."
            # Create database if it doesn't exist
            sudo -u postgres psql -c "SELECT 'CREATE DATABASE sheepskin_db' WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'sheepskin_db')\gexec"
            
            # Create user if it doesn't exist
            sudo -u postgres psql -c "DO \$\$ BEGIN CREATE USER sheepskin_user WITH ENCRYPTED PASSWORD 'Kingragnar123'; EXCEPTION WHEN duplicate_object THEN null; END \$\$;"
            
            # Grant privileges
            sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE sheepskin_db TO sheepskin_user;"
            sudo -u postgres psql -d sheepskin_db -c "GRANT ALL ON SCHEMA public TO sheepskin_user;"
            sudo -u postgres psql -d sheepskin_db -c "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO sheepskin_user;"
            sudo -u postgres psql -d sheepskin_db -c "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO sheepskin_user;"
          fi

          # Install dependencies
          echo "Installing dependencies..."
          npm ci --omit=dev

          # Generate Prisma client
          echo "Generating Prisma client..."
          npx prisma generate

          # Run database migrations
          echo "Running database migrations..."
          npx prisma migrate deploy

          # Build the application
          echo "Building application..."
          npm run build

          # Install PM2 if needed
          if ! command -v pm2 &> /dev/null; then
            echo "Installing PM2..."
            sudo npm install -g pm2
          fi

          # Restart application
          echo "Restarting application..."
          pm2 restart sheepskin-app || pm2 start npm --name "sheepskin-app" -- start

          # Save PM2 configuration
          pm2 save

          # Health check
          echo "Performing health check..."
          sleep 10
          for i in {1..5}; do
            if curl -f http://localhost:3000 >/dev/null 2>&1; then
              echo "Health check passed"
              break
            fi
            if [ $i -eq 5 ]; then
              echo "Health check failed after 5 attempts"
              exit 1
            fi
            echo "Health check attempt $i failed, retrying in 10 seconds..."
            sleep 10
          done

          echo "Deployment completed successfully!"
          DEPLOY_EOF

          # Replace placeholders with actual values
          sed -i "s|DATABASE_URL_PLACEHOLDER|$DATABASE_URL|g" ~/deploy_script.sh
          sed -i "s|NEXTAUTH_SECRET_PLACEHOLDER|$NEXTAUTH_SECRET|g" ~/deploy_script.sh
          sed -i "s|NEXTAUTH_URL_PLACEHOLDER|$NEXTAUTH_URL|g" ~/deploy_script.sh
          sed -i "s|PAT_TOKEN_PLACEHOLDER|$PAT_TOKEN|g" ~/deploy_script.sh

          # Execute deployment on EC2
          scp -o StrictHostKeyChecking=no -i ~/.ssh/ec2_key.pem ~/deploy_script.sh $EC2_USER@$EC2_HOST:/tmp/
          ssh -o StrictHostKeyChecking=no -i ~/.ssh/ec2_key.pem $EC2_USER@$EC2_HOST "chmod +x /tmp/deploy_script.sh && /tmp/deploy_script.sh"

      - name: Rollback on deployment failure
        if: failure()
        env:
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_KEY: ${{ secrets.EC2_KEY }}
        run: |
          # Create temporary directory for SSH key
          mkdir -p ~/.ssh
          echo "$EC2_KEY" > ~/.ssh/ec2_key.pem
          chmod 600 ~/.ssh/ec2_key.pem

          cat > ~/rollback_script.sh << 'ROLLBACK_SCRIPT'
          #!/bin/bash
          set -e

          echo "Performing rollback..."

          if [ -d "/home/<USER>/sheepskin-app" ]; then
            cd /home/<USER>/sheepskin-app
            
            # Look for the most recent backup
            LATEST_BACKUP=$(ls -t /tmp/sheepskin-backup-* 2>/dev/null | head -1)
            if [ -n "$LATEST_BACKUP" ] && [ -d "$LATEST_BACKUP/.next" ]; then
              echo "Restoring from backup: $LATEST_BACKUP"
              rm -rf .next 2>/dev/null || true
              cp -r "$LATEST_BACKUP/.next" .next
              if [ -f "$LATEST_BACKUP/.env" ]; then
                cp "$LATEST_BACKUP/.env" .env
              fi
              pm2 restart sheepskin-app
              echo "Rollback completed"
            else
              echo "No backup found, reverting to previous commit..."
              git reset --hard HEAD~1
              npm ci --omit=dev
              npm run build
              pm2 restart sheepskin-app
              echo "Rollback to previous commit completed"
            fi
          else
            echo "Application directory not found, rollback not possible"
          fi
          ROLLBACK_SCRIPT

          scp -o StrictHostKeyChecking=no -i ~/.ssh/ec2_key.pem ~/rollback_script.sh $EC2_USER@$EC2_HOST:/tmp/
          ssh -o StrictHostKeyChecking=no -i ~/.ssh/ec2_key.pem $EC2_USER@$EC2_HOST "chmod +x /tmp/rollback_script.sh && /tmp/rollback_script.sh"
