# Sheepskin Collection Platform

[![CI/CD Pipeline](https://github.com/your-username/sheepskin-app/actions/workflows/deploy.yml/badge.svg)](https://github.com/your-username/sheepskin-app/actions/workflows/deploy.yml)

A Next.js web application that connects sheepskin donors with collectors during Eid al-Adha in Tunisia, promoting sustainable reuse of animal hides.

## Features

- **User Authentication**: Secure login/registration system with role-based access
- **Dashboard System**:
  - Donor Dashboard: Create and manage donations
  - Collector Dashboard: View and collect available donations
  - Admin Dashboard: Manage users and oversee operations
- **Interactive Maps**: OpenStreetMap integration for location-based services
- **Geolocation Services**: Address search and reverse geocoding using Nominatim API
- **Real-time Updates**: Track donation status and collection progress

## Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Radix UI Components
- **Authentication**: NextAuth.js
- **Database**: PostgreSQL with Prisma ORM
- **Maps**: MapLibre GL JS with OpenStreetMap tiles
- **Geocoding**: OpenStreetMap Nominatim API

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- PostgreSQL database
- Environment variables (see `.env.example`)

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd sheepskin-app
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env
# Edit .env with your database URL and other settings
```

4. Set up the database:

```bash
npx prisma generate
npx prisma migrate dev
npx prisma db seed
```

5. Run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## Environment Variables

Create a `.env` file with the following variables:

```env
DATABASE_URL="postgresql://username:password@localhost:5432/sheepskin_db"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

## Database Schema

The application uses the following main models:

- **User**: Authentication and user management
- **Donation**: Sheepskin donation records
- **Collection**: Collection requests and tracking

## API Endpoints

- `/api/auth/*` - Authentication endpoints
- `/api/donations` - Donation management
- `/api/collections` - Collection management

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## AWS EC2 Deployment Guide

This guide provides step-by-step instructions for deploying the Sheepskin Collection Platform on AWS EC2.

### Prerequisites for Deployment

- AWS Account with EC2 access
- Basic knowledge of Linux commands
- SSH client (PuTTY for Windows or built-in SSH)
- Domain name (optional, for custom URL)

### Step 1: Create EC2 Instance

1. **Log into AWS Console**

   - Go to [AWS Console](https://aws.amazon.com/console/)
   - Navigate to EC2 service

2. **Launch Instance**

   - Click "Launch Instance"
   - **Name**: `sheepskin-app-server`
   - **AMI**: Choose "Ubuntu Server 22.04 LTS (HVM), SSD Volume Type"
   - **Instance Type**: `t3.small` or `t3.medium` (recommended for production)
   - **Key Pair**: Create new key pair or use existing
     - Name: `sheepskin-app-key`
     - Type: RSA
     - Format: .pem (for SSH) or .ppk (for PuTTY)
     - **IMPORTANT**: Download and save the key file securely

3. **Configure Security Group**

   - Create new security group: `sheepskin-app-sg`
   - Add the following inbound rules:
     ```
     Type: SSH, Protocol: TCP, Port: 22, Source: 0.0.0.0/0
     Type: HTTP, Protocol: TCP, Port: 80, Source: 0.0.0.0/0
     Type: HTTPS, Protocol: TCP, Port: 443, Source: 0.0.0.0/0
     Type: Custom TCP, Protocol: TCP, Port: 3000, Source: 0.0.0.0/0
     Type: Custom TCP, Protocol: TCP, Port: 5432, Source: Your IP (for PostgreSQL)
     ```

4. **Configure Storage**

   - Root volume: 20 GB gp3 (minimum)
   - For production: 30-50 GB recommended

5. **Launch Instance**
   - Review settings and click "Launch Instance"
   - Wait for instance to be in "running" state

### Step 2: Connect to EC2 Instance

**For Windows (using PuTTY):**

```bash
# Convert .pem to .ppk using PuTTYgen if needed
# Connect using PuTTY with:
# Host: your-ec2-public-ip
# Port: 22
# Auth: Browse to your .ppk file
```

**For Linux/Mac/Windows (using SSH):**

```bash
# Make key file readable only by you
chmod 400 sheepskin-app-key.pem

# Connect to instance
ssh -i "sheepskin-app-key.pem" ubuntu@your-ec2-public-ip
```

### Step 3: Server Setup

1. **Update System**

```bash
sudo apt update && sudo apt upgrade -y
```

2. **Install Node.js 18+**

```bash
# Install Node.js via NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

3. **Install PostgreSQL**

```bash
sudo apt install postgresql postgresql-contrib -y
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Set up PostgreSQL
sudo -u postgres psql
```

In PostgreSQL prompt:

```sql
CREATE DATABASE sheepskin_db;
CREATE USER sheepskin_user WITH ENCRYPTED PASSWORD 'your_strong_password';
GRANT ALL PRIVILEGES ON DATABASE sheepskin_db TO sheepskin_user;
\q
```

4. **Install PM2 (Process Manager)**

```bash
sudo npm install -g pm2
```

5. **Install Nginx (Reverse Proxy)**

```bash
sudo apt install nginx -y
sudo systemctl start nginx
sudo systemctl enable nginx
```

### Step 4: Deploy Application

1. **Clone Repository**

```bash
cd /home/<USER>
git clone https://github.com/your-username/sheepskin-app.git
cd sheepskin-app
```

2. **Install Dependencies**

```bash
npm install
```

3. **Set Up Environment Variables**

```bash
# Create production environment file
nano .env.production

# Add the following variables:
DATABASE_URL="postgresql://sheepskin_user:your_strong_password@localhost:5432/sheepskin_db"
NEXTAUTH_SECRET="your-very-long-random-secret-key-here"
NEXTAUTH_URL="http://your-ec2-public-ip"
NODE_ENV="production"

# IMPORTANT: If your password contains special characters like $, %, @, etc.
# you need to URL encode them:
# $ becomes %24
# % becomes %25
# @ becomes %40
```

4. **Set Up Database**

```bash
npx prisma generate
npx prisma migrate deploy
npx prisma db seed
```

5. **Build Application**

```bash
npm run build
```

6. **Start Application with PM2**

```bash
# Start the application
pm2 start npm --name "sheepskin-app" -- start

# Save PM2 configuration
pm2 save

# Set up PM2 to start on boot
pm2 startup
# Follow the instructions provided by the command above
```

### Step 5: Configure Nginx

1. **Create Nginx Configuration**

```bash
sudo nano /etc/nginx/sites-available/sheepskin-app
```

2. **Add Configuration**

```nginx
server {
    listen 80;
    server_name your-ec2-public-ip your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

3. **Enable Site and Restart Nginx**

```bash
sudo ln -s /etc/nginx/sites-available/sheepskin-app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### Step 6: Set Up SSL Certificate (Optional but Recommended)

1. **Install Certbot**

```bash
sudo apt install certbot python3-certbot-nginx -y
```

2. **Obtain SSL Certificate**

```bash
sudo certbot --nginx -d your-domain.com
```

3. **Auto-renewal Setup**

```bash
sudo crontab -e
# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

### Step 7: Configure Firewall

```bash
# Enable UFW firewall
sudo ufw enable

# Allow necessary ports
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow 3000

# Check status
sudo ufw status
```

### Step 8: Final Configuration

1. **Update Environment Variable**

```bash
nano .env.production
# Update NEXTAUTH_URL to your domain or public IP
NEXTAUTH_URL="https://your-domain.com"  # or http://your-ec2-public-ip
```

2. **Restart Application**

```bash
pm2 restart sheepskin-app
```

### Step 9: Access Your Application

Your application should now be accessible at:

- **HTTP**: `http://your-ec2-public-ip`
- **HTTPS** (if SSL configured): `https://your-domain.com`

### Useful Commands for Management

```bash
# Check application status
pm2 status

# View application logs
pm2 logs sheepskin-app

# Restart application
pm2 restart sheepskin-app

# Stop application
pm2 stop sheepskin-app

# Check Nginx status
sudo systemctl status nginx

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log

# Check PostgreSQL status
sudo systemctl status postgresql

# Connect to PostgreSQL
sudo -u postgres psql sheepskin_db
```

### Troubleshooting

1. **Application not starting:**

   - Check logs: `pm2 logs sheepskin-app`
   - Verify environment variables
   - Check database connection

2. **502 Bad Gateway:**

   - Ensure application is running on port 3000
   - Check Nginx configuration
   - Verify proxy settings

3. **Database connection issues:**

   - Check PostgreSQL is running
   - Verify credentials in `.env.production`
   - Ensure database exists

4. **SSL Certificate issues:**

   - Verify domain DNS points to EC2 public IP
   - Check certbot logs: `sudo journalctl -u certbot`


  

### Production Best Practices

1. **Security:**

   - Use strong passwords
   - Regularly update system packages
   - Configure fail2ban for SSH protection
   - Use security groups to restrict access

2. **Monitoring:**

   - Set up CloudWatch monitoring
   - Configure application logs
   - Monitor disk space and memory usage

3. **Backups:**

   - Regular database backups
   - Application code backups
   - Environment configuration backups

4. **Updates:**
   - Plan regular maintenance windows
   - Test updates in staging environment
   - Keep dependencies updated

### Cost Optimization

- Use reserved instances for long-term deployment
- Configure auto-scaling if needed
- Monitor usage with AWS Cost Explorer
- Consider using Application Load Balancer for high availability
