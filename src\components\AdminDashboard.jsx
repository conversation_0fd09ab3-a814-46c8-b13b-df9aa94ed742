"use client";

import { useState } from "react";
import ProtectedRoute from "@/components/ProtectedRoute";
import { useAuth } from "@/contexts/AuthContext";

export default function AdminDashboard() {
  const [language, setLanguage] = useState("en");
  const [activeTab, setActiveTab] = useState("dashboard");

  const { user, profile, signOut } = useAuth();

  const [users, setUsers] = useState([
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "<PERSON><PERSON>",
      status: "Active",
    },
    {
      id: 2,
      name: "<PERSON><PERSON>rabels<PERSON>",
      email: "<EMAIL>",
      role: "Collector",
      status: "Active",
    },
    {
      id: 3,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Don<PERSON>",
      status: "Inactive",
    },
  ]);

  const [donations, setDonations] = useState([
    {
      id: 101,
      donor: "<PERSON>",
      hides: 3,
      location: "Tunis, Bab Bhar",
      status: "Available",
    },
    {
      id: 102,
      donor: "<PERSON><PERSON>",
      hides: 2,
      location: "Sfax, Medina",
      status: "Collected",
    },
    {
      id: 103,
      donor: "<PERSON>",
      hides: 5,
      location: "Sousse, Centre Ville",
      status: "Claimed",
    },
  ]);

  const [collections, setCollections] = useState([
    {
      id: 1,
      collector: "Fatma Trabelsi",
      donationId: 102,
      status: "Completed",
    },
    {
      id: 2,
      collector: "Ali Mansouri",
      donationId: 103,
      status: "In Progress",
    },
  ]);

  const translations = {
    en: {
      dashboard: "Dashboard",
      users: "Users",
      donations: "Donations",
      collections: "Collections",
      settings: "Settings",
      adminDashboard: "Admin Dashboard",
      totalUsers: "Total Users",
      totalDonations: "Total Donations",
      totalCollections: "Total Collections",
      pendingCollections: "Pending Collections",
      id: "ID",
      name: "Name",
      email: "Email",
      role: "Role",
      status: "Status",
      actions: "Actions",
      edit: "Edit",
      delete: "Delete",
      donor: "Donor",
      collector: "Collector",
      admin: "Admin",
      active: "Active",
      inactive: "Inactive",
      available: "Available",
      claimed: "Claimed",
      collected: "Collected",
      completed: "Completed",
      inProgress: "In Progress",
      location: "Location",
      numberOfHides: "Number of Hides",
      donationId: "Donation ID",
      pickupTime: "Pickup Time",
    },
    fr: {
      dashboard: "Tableau de Bord",
      users: "Utilisateurs",
      donations: "Dons",
      collections: "Collections",
      settings: "Paramètres",
      adminDashboard: "Tableau de Bord Admin",
      totalUsers: "Total Utilisateurs",
      totalDonations: "Total Dons",
      totalCollections: "Total Collections",
      pendingCollections: "Collections en Attente",
      id: "ID",
      name: "Nom",
      email: "Email",
      role: "Rôle",
      status: "Statut",
      actions: "Actions",
      edit: "Modifier",
      delete: "Supprimer",
      donor: "Donateur",
      collector: "Collecteur",
      admin: "Admin",
      active: "Actif",
      inactive: "Inactif",
      available: "Disponible",
      claimed: "Réclamé",
      collected: "Collecté",
      completed: "Terminé",
      inProgress: "En Cours",
      location: "Emplacement",
      numberOfHides: "Nombre de Peaux",
      donationId: "ID du Don",
      pickupTime: "Heure de Ramassage",
    },
    ar: {
      dashboard: "لوحة التحكم",
      users: "المستخدمون",
      donations: "التبرعات",
      collections: "المجموعات",
      settings: "الإعدادات",
      adminDashboard: "لوحة تحكم المدير",
      totalUsers: "إجمالي المستخدمين",
      totalDonations: "إجمالي التبرعات",
      totalCollections: "إجمالي المجموعات",
      pendingCollections: "المجموعات المعلقة",
      id: "المعرف",
      name: "الاسم",
      email: "البريد الإلكتروني",
      role: "الدور",
      status: "الحالة",
      actions: "الإجراءات",
      edit: "تعديل",
      delete: "حذف",
      donor: "متبرع",
      collector: "جامع",
      admin: "مدير",
      active: "نشط",
      inactive: "غير نشط",
      available: "متاح",
      claimed: "مطالب به",
      collected: "تم الجمع",
      completed: "مكتمل",
      inProgress: "قيد التنفيذ",
      location: "الموقع",
      numberOfHides: "عدد الجلود",
      donationId: "معرف التبرع",
      pickupTime: "وقت الاستلام",
    },
  };

  const t = (key) => translations[language][key] || key;

  const deleteUser = (id) => {
    setUsers(users.filter((u) => u.id !== id));
  };

  const deleteDonation = (id) => {
    setDonations(donations.filter((d) => d.id !== id));
  };

  const deleteCollection = (id) => {
    setCollections(collections.filter((c) => c.id !== id));
  };

  const stats = {
    totalUsers: users.length,
    totalDonations: donations.length,
    totalCollections: collections.length,
    pendingCollections: collections.filter((c) => c.status === "In Progress")
      .length,
  };

  return (
    <ProtectedRoute requiredRole="ADMIN">
      <div
        className={`min-h-screen bg-gray-50 ${
          language === "ar" ? "rtl" : "ltr"
        }`}
      >
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white shadow-lg min-h-screen">
            <div className="p-6">
              <h1 className="text-2xl font-bold text-gray-900">
                {t("adminDashboard")}
              </h1>
              <div>
                <p>
                  {profile?.firstName} {profile?.lastName}
                </p>
                <button onClick={signOut}>Sign Out</button>
              </div>

              {/* Language Selector */}
              <div className="mt-4">
                <select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  className="w-full bg-white border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-red-500"
                  aria-label="Select language"
                >
                  <option value="en">English</option>
                  <option value="fr">Français</option>
                  <option value="ar">العربية</option>
                </select>
              </div>
            </div>

            <nav className="mt-6">
              {[
                "dashboard",
                "users",
                "donations",
                "collections",
                "settings",
              ].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`w-full text-left px-6 py-3 text-sm font-medium transition-colors ${
                    activeTab === tab
                      ? "bg-red-50 text-red-700 border-r-2 border-red-500"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                  aria-label={t(tab)}
                >
                  {t(tab)}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-8">
            {activeTab === "dashboard" && (
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">
                  {t("dashboard")}
                </h2>

                {/* Stats Cards */}
                <div className="grid md:grid-cols-4 gap-6 mb-8">
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-700">
                      {t("totalUsers")}
                    </h3>
                    <p className="text-3xl font-bold text-blue-600">
                      {stats.totalUsers}
                    </p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-700">
                      {t("totalDonations")}
                    </h3>
                    <p className="text-3xl font-bold text-green-600">
                      {stats.totalDonations}
                    </p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-700">
                      {t("totalCollections")}
                    </h3>
                    <p className="text-3xl font-bold text-purple-600">
                      {stats.totalCollections}
                    </p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-700">
                      {t("pendingCollections")}
                    </h3>
                    <p className="text-3xl font-bold text-yellow-600">
                      {stats.pendingCollections}
                    </p>
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-white rounded-lg shadow p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      Recent {t("users")}
                    </h3>
                    <div className="space-y-3">
                      {users.slice(0, 3).map((user) => (
                        <div
                          key={user.id}
                          className="flex justify-between items-center"
                        >
                          <div>
                            <p className="font-medium text-gray-900">
                              {user.name}
                            </p>
                            <p className="text-sm text-gray-500">
                              {t(user.role.toLowerCase())}
                            </p>
                          </div>
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium ${
                              user.status === "Active"
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {t(user.status.toLowerCase())}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      Recent {t("donations")}
                    </h3>
                    <div className="space-y-3">
                      {donations.slice(0, 3).map((donation) => (
                        <div
                          key={donation.id}
                          className="flex justify-between items-center"
                        >
                          <div>
                            <p className="font-medium text-gray-900">
                              #{donation.id}
                            </p>
                            <p className="text-sm text-gray-500">
                              {donation.hides} hides - {donation.location}
                            </p>
                          </div>
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium ${
                              donation.status === "Available"
                                ? "bg-green-100 text-green-800"
                                : donation.status === "Claimed"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {t(donation.status.toLowerCase())}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "users" && (
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">
                  {t("users")}
                </h2>

                <div className="bg-white rounded-lg shadow">
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b bg-gray-50">
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("id")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("name")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("email")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("role")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("status")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("actions")}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {users.map((user) => (
                          <tr
                            key={user.id}
                            className="border-b hover:bg-gray-50"
                          >
                            <td className="py-4 px-6">{user.id}</td>
                            <td className="py-4 px-6">{user.name}</td>
                            <td className="py-4 px-6">{user.email}</td>
                            <td className="py-4 px-6">
                              <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {t(user.role.toLowerCase())}
                              </span>
                            </td>
                            <td className="py-4 px-6">
                              <span
                                className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  user.status === "Active"
                                    ? "bg-green-100 text-green-800"
                                    : "bg-red-100 text-red-800"
                                }`}
                              >
                                {t(user.status.toLowerCase())}
                              </span>
                            </td>
                            <td className="py-4 px-6">
                              <div className="flex space-x-2">
                                <button
                                  className="text-blue-600 hover:text-blue-800 font-medium"
                                  aria-label={`${t("edit")} user ${user.name}`}
                                >
                                  {t("edit")}
                                </button>
                                <button
                                  onClick={() => deleteUser(user.id)}
                                  className="text-red-600 hover:text-red-800 font-medium"
                                  aria-label={`${t("delete")} user ${
                                    user.name
                                  }`}
                                >
                                  {t("delete")}
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "donations" && (
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">
                  {t("donations")}
                </h2>

                <div className="bg-white rounded-lg shadow">
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b bg-gray-50">
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("id")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("donor")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("numberOfHides")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("location")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("status")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("actions")}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {donations.map((donation) => (
                          <tr
                            key={donation.id}
                            className="border-b hover:bg-gray-50"
                          >
                            <td className="py-4 px-6">{donation.id}</td>
                            <td className="py-4 px-6">{donation.donor}</td>
                            <td className="py-4 px-6">{donation.hides}</td>
                            <td className="py-4 px-6">{donation.location}</td>
                            <td className="py-4 px-6">
                              <span
                                className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  donation.status === "Available"
                                    ? "bg-green-100 text-green-800"
                                    : donation.status === "Claimed"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : "bg-gray-100 text-gray-800"
                                }`}
                              >
                                {t(donation.status.toLowerCase())}
                              </span>
                            </td>
                            <td className="py-4 px-6">
                              <div className="flex space-x-2">
                                <button
                                  className="text-blue-600 hover:text-blue-800 font-medium"
                                  aria-label={`${t("edit")} donation ${
                                    donation.id
                                  }`}
                                >
                                  {t("edit")}
                                </button>
                                <button
                                  onClick={() => deleteDonation(donation.id)}
                                  className="text-red-600 hover:text-red-800 font-medium"
                                  aria-label={`${t("delete")} donation ${
                                    donation.id
                                  }`}
                                >
                                  {t("delete")}
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "collections" && (
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">
                  {t("collections")}
                </h2>

                <div className="bg-white rounded-lg shadow">
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b bg-gray-50">
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("id")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("collector")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("donationId")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("status")}
                          </th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">
                            {t("actions")}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {collections.map((collection) => (
                          <tr
                            key={collection.id}
                            className="border-b hover:bg-gray-50"
                          >
                            <td className="py-4 px-6">{collection.id}</td>
                            <td className="py-4 px-6">
                              {collection.collector}
                            </td>
                            <td className="py-4 px-6">
                              {collection.donationId}
                            </td>
                            <td className="py-4 px-6">
                              <span
                                className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  collection.status === "Completed"
                                    ? "bg-green-100 text-green-800"
                                    : "bg-yellow-100 text-yellow-800"
                                }`}
                              >
                                {t(
                                  collection.status
                                    .toLowerCase()
                                    .replace(" ", "")
                                )}
                              </span>
                            </td>
                            <td className="py-4 px-6">
                              <div className="flex space-x-2">
                                <button
                                  className="text-blue-600 hover:text-blue-800 font-medium"
                                  aria-label={`${t("edit")} collection ${
                                    collection.id
                                  }`}
                                >
                                  {t("edit")}
                                </button>
                                <button
                                  onClick={() =>
                                    deleteCollection(collection.id)
                                  }
                                  className="text-red-600 hover:text-red-800 font-medium"
                                  aria-label={`${t("delete")} collection ${
                                    collection.id
                                  }`}
                                >
                                  {t("delete")}
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "settings" && (
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">
                  {t("settings")}
                </h2>
                <div className="bg-white rounded-lg shadow p-6">
                  <p className="text-gray-600">
                    System settings will be implemented here.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
