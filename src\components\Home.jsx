"use client";

import { useState } from "react";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";

export default function Home() {
  const [language, setLanguage] = useState("en");
  const { user, signOut } = useAuth();

  const translations = {
    en: {
      title: "Sheepskin Collection Platform",
      subtitle:
        "Connecting donors and collectors for sustainable reuse during Eid al-Adha",
      donateButton: "Donate Hides",
      collectButton: "Collect Hides",
      aboutTitle: "About Our Mission",
      aboutText:
        "We facilitate the collection and reuse of sheepskin during Eid al-Adha, promoting sustainability and community cooperation in Tunisia.",
      home: "Home",
      register: "Register",
      login: "Login",
      about: "About",
      contact: "Contact",
      terms: "Terms",
      howItWorks: "How It Works",
      step1: "Donors post available hides",
      step2: "Collectors view locations on map",
      step3: "Coordinate pickup times",
      step4: "Complete collection process",
    },
    fr: {
      title: "Plateforme de Collecte de Peaux",
      subtitle:
        "Connecter les donateurs et collecteurs pour une réutilisation durable pendant l'Aïd al-Adha",
      donateButton: "Donner des Peaux",
      collectButton: "Collecter des Peaux",
      aboutTitle: "À Propos de Notre Mission",
      aboutText:
        "Nous facilitons la collecte et la réutilisation des peaux de mouton pendant l'Aïd al-Adha, promouvant la durabilité et la coopération communautaire en Tunisie.",
      home: "Accueil",
      register: "S'inscrire",
      login: "Connexion",
      about: "À Propos",
      contact: "Contact",
      terms: "Conditions",
      howItWorks: "Comment Ça Marche",
      step1: "Les donateurs publient les peaux disponibles",
      step2: "Les collecteurs voient les emplacements sur la carte",
      step3: "Coordonner les heures de ramassage",
      step4: "Terminer le processus de collecte",
    },
    ar: {
      title: "منصة جمع جلود الأضاحي",
      subtitle:
        "ربط المتبرعين والجامعين لإعادة الاستخدام المستدام خلال عيد الأضحى",
      donateButton: "تبرع بالجلود",
      collectButton: "اجمع الجلود",
      aboutTitle: "حول مهمتنا",
      aboutText:
        "نحن نسهل جمع وإعادة استخدام جلود الأغنام خلال عيد الأضحى، مما يعزز الاستدامة والتعاون المجتمعي في تونس.",
      home: "الرئيسية",
      register: "التسجيل",
      login: "تسجيل الدخول",
      about: "حول",
      contact: "اتصل",
      terms: "الشروط",
      howItWorks: "كيف يعمل",
      step1: "المتبرعون ينشرون الجلود المتاحة",
      step2: "الجامعون يرون المواقع على الخريطة",
      step3: "تنسيق أوقات الاستلام",
      step4: "إكمال عملية الجمع",
    },
  };

  const t = (key) => translations[language][key] || key;

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-amber-50 to-orange-100 ${
        language === "ar" ? "rtl" : "ltr"
      }`}
    >
      {/* Header */}
      <header className="bg-white shadow-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            {/* Logo */}
            <div className="flex items-center">
              <div className="w-10 h-10 bg-amber-600 rounded-full flex items-center justify-center">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </div>
              <h1 className="ml-3 text-xl font-bold text-gray-900">
                {t("title")}
              </h1>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              <Link
                href="/"
                className="text-gray-700 hover:text-amber-600 font-medium"
                aria-label={t("home")}
              >
                {t("home")}
              </Link>
              {!user ? (
                <>
                  <Link
                    href="/register"
                    className="text-gray-700 hover:text-amber-600 font-medium"
                    aria-label={t("register")}
                  >
                    {t("register")}
                  </Link>
                  <Link
                    href="/login"
                    className="text-gray-700 hover:text-amber-600 font-medium"
                    aria-label={t("login")}
                  >
                    {t("login")}
                  </Link>
                </>
              ) : (
                <>
                  <span className="text-gray-700">Welcome, {user?.name}</span>
                  <button
                    onClick={() => signOut()}
                    className="text-gray-700 hover:text-amber-600 font-medium"
                  >
                    Logout
                  </button>
                </>
              )}
              <Link
                href="/about"
                className="text-gray-700 hover:text-amber-600 font-medium"
                aria-label={t("about")}
              >
                {t("about")}
              </Link>
            </nav>

            {/* Language Selector */}
            <div className="relative">
              <select
                value={language}
                onChange={(e) => setLanguage(e.target.value)}
                className="bg-white border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500"
                aria-label="Select language"
              >
                <option value="en">English</option>
                <option value="fr">Français</option>
                <option value="ar">العربية</option>
              </select>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            {t("title")}
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            {t("subtitle")}
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            {user ? (
              // Show dashboard links for authenticated users
              <>
                {user?.role === "DONOR" && (
                  <Link
                    href="/donor-dashboard"
                    className="bg-green-500 hover:bg-green-600 text-white py-4 px-8 rounded-lg text-lg font-semibold transition-colors duration-200 shadow-lg"
                  >
                    {t("donateButton")}
                  </Link>
                )}
                {user?.role === "COLLECTOR" && (
                  <Link
                    href="/collector-map"
                    className="bg-blue-500 hover:bg-blue-600 text-white py-4 px-8 rounded-lg text-lg font-semibold transition-colors duration-200 shadow-lg"
                  >
                    {t("collectButton")}
                  </Link>
                )}
                {user?.role === "ADMIN" && (
                  <Link
                    href="/admin-dashboard"
                    className="bg-red-500 hover:bg-red-600 text-white py-4 px-8 rounded-lg text-lg font-semibold transition-colors duration-200 shadow-lg"
                  >
                    Admin Dashboard
                  </Link>
                )}
              </>
            ) : (
              // Show registration links for non-authenticated users
              <>
                <Link
                  href="/register"
                  className="bg-green-500 hover:bg-green-600 text-white py-4 px-8 rounded-lg text-lg font-semibold transition-colors duration-200 shadow-lg"
                  aria-label={t("donateButton")}
                >
                  {t("donateButton")}
                </Link>
                <Link
                  href="/register"
                  className="bg-blue-500 hover:bg-blue-600 text-white py-4 px-8 rounded-lg text-lg font-semibold transition-colors duration-200 shadow-lg"
                  aria-label={t("collectButton")}
                >
                  {t("collectButton")}
                </Link>
              </>
            )}
          </div>
        </div>

        {/* How It Works Section */}
        <section className="bg-white rounded-xl shadow-lg p-8 mb-12">
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-8">
            {t("howItWorks")}
          </h3>
          <div className="grid md:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="text-center">
                <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-amber-600">
                    {step}
                  </span>
                </div>
                <p className="text-gray-700">{t(`step${step}`)}</p>
              </div>
            ))}
          </div>
        </section>

        {/* About Section */}
        <section className="bg-amber-50 rounded-xl p-8">
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-6">
            {t("aboutTitle")}
          </h3>
          <p className="text-lg text-gray-700 text-center max-w-4xl mx-auto leading-relaxed">
            {t("aboutText")}
          </p>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <p>
                &copy; 2024 Sheepskin Collection Platform. All rights reserved.
              </p>
            </div>
            <div className="flex space-x-6">
              <Link
                href="/contact"
                className="hover:text-amber-400 transition-colors"
                aria-label={t("contact")}
              >
                {t("contact")}
              </Link>
              <Link
                href="/terms"
                className="hover:text-amber-400 transition-colors"
                aria-label={t("terms")}
              >
                {t("terms")}
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
