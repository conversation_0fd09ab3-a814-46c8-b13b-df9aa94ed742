version: "3.8"

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: sheepskin-db
    environment:
      POSTGRES_DB: sheepskin
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-your_secure_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - sheepskin-network
    restart: unless-stopped

  # Next.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sheepskin-app
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${DB_PASSWORD:-your_secure_password}@postgres:5432/sheepskin
      - NEXTAUTH_URL=${NEXTAUTH_URL:-http://localhost:3000}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
    ports:
      - "3000:3000"
    depends_on:
      - postgres
    networks:
      - sheepskin-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  sheepskin-network:
    driver: bridge
