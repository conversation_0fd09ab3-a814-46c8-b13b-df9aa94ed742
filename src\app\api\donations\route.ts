import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET: Fetch all donations for the authenticated donor
export async function GET(request: NextRequest) {
  try {
    // Get the session to verify authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify the user is a donor
    if (session.user.role !== "DONOR") {
      return NextResponse.json(
        { error: "Access denied. Only donors can view donations." },
        { status: 403 }
      );
    }

    // Fetch donations for the authenticated donor
    const donations = await prisma.donation.findMany({
      where: {
        donorId: session.user.id,
      },
      orderBy: {
        createdAt: "desc", // Most recent first
      },
    });

    return NextResponse.json({ donations });
  } catch (error) {
    console.error("Error fetching donations:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST: Create a new donation
export async function POST(request: NextRequest) {
  try {
    // Get the session to verify authentication
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify the user is a donor
    if (session.user.role !== "DONOR") {
      return NextResponse.json(
        { error: "Access denied. Only donors can create donations." },
        { status: 403 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { numberOfHides, location, collectionTime, imageUrls, description } =
      body;

    // Validate input data
    if (!numberOfHides || !location || !collectionTime) {
      return NextResponse.json(
        {
          error:
            "All fields are required: numberOfHides, location, collectionTime",
        },
        { status: 400 }
      );
    }

    // Validate numberOfHides is a positive integer
    const hidesNum = parseInt(numberOfHides);
    if (isNaN(hidesNum) || hidesNum <= 0) {
      return NextResponse.json(
        { error: "Number of hides must be a positive integer" },
        { status: 400 }
      );
    }

    // Validate location is not empty
    if (typeof location !== "string" || location.trim().length === 0) {
      return NextResponse.json(
        { error: "Location cannot be empty" },
        { status: 400 }
      );
    }

    // Validate collectionTime is a valid date in the future
    const collectionDate = new Date(collectionTime);
    if (isNaN(collectionDate.getTime())) {
      return NextResponse.json(
        { error: "Invalid collection time format" },
        { status: 400 }
      );
    }

    // Check if collection time is in the future
    if (collectionDate <= new Date()) {
      return NextResponse.json(
        { error: "Collection time must be in the future" },
        { status: 400 }
      );
    }

    // Validate imageUrls if provided
    const validImageUrls = Array.isArray(imageUrls)
      ? imageUrls.filter(
          (url) => typeof url === "string" && url.trim().length > 0
        )
      : [];

    // Create the donation in the database
    const donation = await prisma.donation.create({
      data: {
        donorId: session.user.id,
        numberOfHides: hidesNum,
        location: location.trim(),
        collectionTime: collectionDate,
        status: "AVAILABLE", // Default status
        imageUrls: validImageUrls,
        description: description ? description.trim() : null,
      },
    });

    return NextResponse.json(
      {
        message: "Donation created successfully",
        donation,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating donation:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
