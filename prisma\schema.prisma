// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enum for user roles
enum Role {
  DONOR
  COLLECTOR
  ADMIN
}

// Enum for donation status
enum DonationStatus {
  AVAILABLE
  CLAIMED
  COLLECTED
}

// Enum for collection status
enum CollectionStatus {
  PENDING
  COMPLETED
}

// User model for authentication
model User {
  id        String   @id @default(cuid())
  name      String
  email     String   @unique
  password  String   // Plain text for now, will be hashed with bcrypt later
  role      Role     @default(DONOR)
  contact   String?  // Optional phone number
  location  String?  // Optional location info
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  donations Donation[] // One-to-many relation with donations
  collections Collection[] // One-to-many relation with collections (for collectors)

  @@map("users")
}

// Donation model for tracking sheep hide donations
model Donation {
  id             String         @id @default(cuid())
  donorId        String
  numberOfHides  Int
  location       String
  latitude       Float?         // Optional latitude coordinate
  longitude      Float?         // Optional longitude coordinate
  status         DonationStatus @default(AVAILABLE)
  collectionTime DateTime
  imageUrls      String[]       @default([]) // Array of S3 image URLs
  description    String?        // Optional description for the donation
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  donor User @relation(fields: [donorId], references: [id], onDelete: Cascade)
  collections Collection[] // One-to-many relation with collections

  @@map("donations")
}

// Collection model for tracking the collection of donations
model Collection {
  id             String         @id @default(cuid())
  collectorId    String
  donationId     String
  status         CollectionStatus @default(PENDING)
  collectedAt    DateTime?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  collector User @relation(fields: [collectorId], references: [id], onDelete: Cascade)
  donation  Donation @relation(fields: [donationId], references: [id], onDelete: Cascade)

  @@map("collections")
}
