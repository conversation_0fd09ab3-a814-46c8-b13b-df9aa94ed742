import React, { useState } from "react";

interface ImageGalleryProps {
  images: string[];
  alt?: string;
  className?: string;
  maxDisplay?: number;
}

export default function ImageGallery({
  images,
  alt = "Donation image",
  className = "",
  maxDisplay = 3,
}: ImageGalleryProps) {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  if (!images || images.length === 0) {
    return (
      <div className={`text-gray-500 text-sm ${className}`}>
        No images available
      </div>
    );
  }

  const displayImages = images.slice(0, maxDisplay);
  const remainingCount = images.length - maxDisplay;

  return (
    <>
      <div className={`flex gap-2 ${className}`}>
        {displayImages.map((imageUrl, index) => (
          <div
            key={index}
            className="relative w-16 h-16 bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:ring-2 hover:ring-blue-500 transition-all"
            onClick={() => setSelectedImage(imageUrl)}
          >
            <img
              src={imageUrl}
              alt={`${alt} ${index + 1}`}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = "/placeholder.jpg"; // Fallback image
              }}
            />
          </div>
        ))}

        {remainingCount > 0 && (
          <div
            className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-300 transition-colors"
            onClick={() => setSelectedImage(images[maxDisplay])}
          >
            <span className="text-sm font-medium text-gray-600">
              +{remainingCount}
            </span>
          </div>
        )}
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl max-h-full">
            <img
              src={selectedImage}
              alt={alt}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-4 right-4 bg-white bg-opacity-80 hover:bg-opacity-100 text-gray-800 rounded-full w-10 h-10 flex items-center justify-center text-xl font-bold transition-all"
              aria-label="Close image"
            >
              ×
            </button>

            {/* Image Navigation */}
            {images.length > 1 && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                {images.map((imageUrl, index) => (
                  <button
                    key={index}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedImage(imageUrl);
                    }}
                    className={`w-3 h-3 rounded-full transition-all ${
                      imageUrl === selectedImage
                        ? "bg-white"
                        : "bg-white bg-opacity-50 hover:bg-opacity-75"
                    }`}
                    aria-label={`View image ${index + 1}`}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}
